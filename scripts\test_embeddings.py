# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test loading and using the trained hero embeddings.
"""

import logging
import sys
import os
from gensim.models import Word2Vec
from gensim.models import KeyedVectors
import numpy as np

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_embeddings():
    """Test loading and using the hero embeddings."""
    logger.info("Testing hero embeddings")
    
    # Test loading the full Word2Vec model
    try:
        model = Word2Vec.load("models/hero_embeddings.word2vec")
        logger.info(f"✅ Successfully loaded Word2Vec model")
        logger.info(f"Vocabulary size: {len(model.wv.key_to_index)}")
        logger.info(f"Vector size: {model.wv.vector_size}")
    except Exception as e:
        logger.error(f"❌ Failed to load Word2Vec model: {e}")
        return False
    
    # Test loading just the KeyedVectors
    try:
        kv = KeyedVectors.load("models/hero_embeddings.kv")
        logger.info(f"✅ Successfully loaded KeyedVectors")
        logger.info(f"Vocabulary size: {len(kv.key_to_index)}")
        logger.info(f"Vector size: {kv.vector_size}")
    except Exception as e:
        logger.error(f"❌ Failed to load KeyedVectors: {e}")
        return False
    
    # Test getting embeddings for specific heroes
    test_heroes = ['41', '26', '100']  # Heroes we know exist from training
    
    for hero_id in test_heroes:
        if hero_id in kv.key_to_index:
            vector = kv[hero_id]
            logger.info(f"Hero {hero_id} embedding: shape={vector.shape}, mean={vector.mean():.4f}, std={vector.std():.4f}")
        else:
            logger.warning(f"Hero {hero_id} not found in embeddings")
    
    # Test similarity calculation
    if '41' in kv.key_to_index and '26' in kv.key_to_index:
        similarity = kv.similarity('41', '26')
        logger.info(f"Similarity between hero 41 and 26: {similarity:.4f}")
    
    # Test getting embeddings for a sequence (like we'll need for LSTM)
    test_sequence = ['41', '26', '100', '52', '90']
    embeddings_matrix = []
    
    for hero_id in test_sequence:
        if hero_id in kv.key_to_index:
            embeddings_matrix.append(kv[hero_id])
        else:
            # Use zero vector for unknown heroes
            embeddings_matrix.append(np.zeros(kv.vector_size))
            logger.warning(f"Using zero vector for unknown hero {hero_id}")
    
    embeddings_matrix = np.array(embeddings_matrix)
    logger.info(f"Embeddings matrix for sequence: shape={embeddings_matrix.shape}")
    
    logger.info("🎉 Embeddings testing completed successfully!")
    return True

if __name__ == "__main__":
    try:
        success = test_embeddings()
        if not success:
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Testing failed: {e}")
        sys.exit(1)
