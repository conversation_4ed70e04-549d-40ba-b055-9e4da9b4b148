name: Dota 2 Predictor CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11, 3.12]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Create test datasets
      run: |
        # Create minimal test datasets if they don't exist
        mkdir -p dataset/train_data
        python -c "
import pandas as pd
import numpy as np

# Create minimal test dataset for CI
np.random.seed(42)
n_matches = 1000

# Create basic match data
data = {
    'match_id': range(1, n_matches + 1),
    'radiant_win': np.random.choice([0, 1], n_matches)
}

# Add hero IDs for all players
for team in ['radiant', 'dire']:
    for player in range(1, 6):
        data[f'{team}_player_{player}_hero_id'] = np.random.randint(1, 130, n_matches)

# Add some basic stats to make it look like full dataset
for team in ['radiant', 'dire']:
    for player in range(1, 6):
        data[f'{team}_player_{player}_kills'] = np.random.randint(0, 20, n_matches)
        data[f'{team}_player_{player}_deaths'] = np.random.randint(0, 15, n_matches)
        data[f'{team}_player_{player}_assists'] = np.random.randint(0, 25, n_matches)
        data[f'{team}_player_{player}_gold_per_min'] = np.random.randint(200, 800, n_matches)
        data[f'{team}_player_{player}_xp_per_min'] = np.random.randint(300, 900, n_matches)
        data[f'{team}_player_{player}_last_hits'] = np.random.randint(0, 400, n_matches)
        data[f'{team}_player_{player}_denies'] = np.random.randint(0, 50, n_matches)
        data[f'{team}_player_{player}_net_worth'] = np.random.randint(5000, 30000, n_matches)
        data[f'{team}_player_{player}_level'] = np.random.randint(1, 25, n_matches)
        data[f'{team}_player_{player}_hero_damage'] = np.random.randint(0, 50000, n_matches)
        data[f'{team}_player_{player}_tower_damage'] = np.random.randint(0, 10000, n_matches)
        data[f'{team}_player_{player}_teamfight_participation'] = np.random.uniform(0, 1, n_matches)
        data[f'{team}_player_{player}_obs_placed'] = np.random.randint(0, 20, n_matches)
        data[f'{team}_player_{player}_sen_placed'] = np.random.randint(0, 15, n_matches)
        data[f'{team}_player_{player}_roshans_killed'] = np.random.randint(0, 3, n_matches)
        data[f'{team}_player_{player}_hero_healing'] = np.random.randint(0, 20000, n_matches)
        data[f'{team}_player_{player}_player_hero_winrate'] = np.random.uniform(0.3, 0.7, n_matches)

df = pd.DataFrame(data)
df.to_csv('dataset/train_data/all_data_match_predict.csv', index=False)
print(f'Created test dataset with {len(df)} matches and {len(df.columns)} columns')
        "
    
    - name: Create draft-only dataset
      run: |
        python scripts/create_draft_only_dataset.py
    
    - name: Run data integrity tests
      run: |
        python -m pytest tests/data/ -v --tb=short
      env:
        PYTHONPATH: .
    
    - name: Run baseline tests
      run: |
        python -m pytest tests/baseline/ -v --tb=short
      env:
        PYTHONPATH: .
    
    - name: Train draft model for testing
      run: |
        python ml/create_model_draft_phase.py
      env:
        PYTHONPATH: .
    
    - name: Run model v1 tests
      run: |
        python -m pytest tests/model_v1/ -v --tb=short
      env:
        PYTHONPATH: .
    
    - name: Run inference tests (basic)
      run: |
        python -m pytest tests/inference/test_bot_endpoint.py::test_different_hero_combinations -v --tb=short
        python -m pytest tests/inference/test_bot_endpoint.py::test_edge_cases -v --tb=short
      env:
        PYTHONPATH: .
    
    - name: Check for metric regressions
      run: |
        python -c "
import json
import os

# Check if metrics files exist and validate them
metrics_files = [
    'tests/expected/baseline_metrics.json',
    'tests/expected/draft_v1_metrics.json',
    'tests/expected/draft_v1_calibration.json',
    'tests/expected/draft_v1_feature_importance.json'
]

for file_path in metrics_files:
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            metrics = json.load(f)
        print(f'✓ {file_path}: {len(metrics)} metrics')
        
        # Basic validation
        if 'accuracy' in metrics:
            acc = metrics['accuracy']
            assert 0.4 < acc < 0.8, f'Accuracy {acc} outside reasonable range'
        if 'log_loss' in metrics:
            ll = metrics['log_loss']
            assert 0.4 < ll < 1.0, f'Log loss {ll} outside reasonable range'
    else:
        print(f'⚠ {file_path} not found (expected in CI)')

print('✓ Metrics validation passed')
        "
      env:
        PYTHONPATH: .

  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort
    
    - name: Run black (code formatting check)
      run: |
        black --check --diff .
      continue-on-error: true
    
    - name: Run isort (import sorting check)
      run: |
        isort --check-only --diff .
      continue-on-error: true
    
    - name: Run flake8 (linting)
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      continue-on-error: true

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run bandit (security linting)
      run: |
        bandit -r . -f json -o bandit-report.json || true
        bandit -r . --severity-level medium
      continue-on-error: true
    
    - name: Run safety (dependency vulnerability check)
      run: |
        pip install -r requirements.txt
        safety check
      continue-on-error: true
