import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define paths
KAGGLE_DATA_DIR = Path("dataset/kaggle_data")
OUTPUT_DIR = Path('models')
OUTPUT_PATH = OUTPUT_DIR / 'hero_counter_rates.csv'
HEROES_PATH = KAGGLE_DATA_DIR / "Constants" / "Constants.Heroes.csv"

# Specify which months to include
MONTHS = ["2023", "2024", "202501", "202502", "202503", "202504", "202505", "202506", "202507"]

def load_data():
    """Loads all monthly picks/bans and metadata from Kaggle CSVs."""
    picks_bans_dfs = []
    meta_dfs = []
    for month in MONTHS:
        month_dir = KAGGLE_DATA_DIR / month
        picks_bans_file = month_dir / "picks_bans.csv"
        meta_file = month_dir / "main_metadata.csv"
        
        if picks_bans_file.exists() and meta_file.exists():
            logging.info(f"Loading data for month: {month}")
            try:
                picks_bans_dfs.append(pd.read_csv(picks_bans_file))
                meta_dfs.append(pd.read_csv(meta_file, usecols=["match_id", "radiant_win"]))
            except Exception as e:
                logging.error(f"Could not read CSVs in {month}: {e}")
        else:
            logging.warning(f"Warning: missing data for month {month}")
            
    if not picks_bans_dfs or not meta_dfs:
        return None, None

    picks_bans_df = pd.concat(picks_bans_dfs, ignore_index=True)
    meta_df = pd.concat(meta_dfs, ignore_index=True).drop_duplicates(subset=["match_id"])
    return picks_bans_df, meta_df

def get_hero_ids():
    """Get a list of all valid hero IDs."""
    if not HEROES_PATH.exists():
        logging.error(f"Hero constants file not found at: {HEROES_PATH}")
        return []
    heroes_df = pd.read_csv(HEROES_PATH)
    return sorted(heroes_df['id'].unique())

def log5(pA, pB):
    """Calculates the Log5 formula for win probability of A vs B."""
    return (pA - pA * pB) / (pA + pB - 2 * pA * pB)

def main():
    """Main function to compute and save the hero counter-rate matrix."""
    logging.info("Starting hero counter-rate computation...")
    
    picks_bans, meta = load_data()
    if picks_bans is None:
        logging.error("No picks/bans data loaded. Aborting.")
        return

    # Merge data to link picks with win outcomes
    merged_df = pd.merge(picks_bans, meta, on='match_id', how='inner')
    picks_df = merged_df[merged_df['is_pick']].copy()

    # Determine if the hero's team won
    # Team 0 is Radiant, Team 1 is Dire
    picks_df['won'] = ((picks_df['team'] == 0) & (picks_df['radiant_win'] == 1)) | \
                      ((picks_df['team'] == 1) & (picks_df['radiant_win'] == 0))

    # Calculate hero appearances and wins
    appearances = picks_df['hero_id'].value_counts()
    wins = picks_df[picks_df['won']]['hero_id'].value_counts()

    # Combine into a single DataFrame
    win_rates = pd.DataFrame({'appearances': appearances, 'wins': wins}).fillna(0)
    
    # Apply Bayesian smoothing to win rates
    # Add pseudo-counts (alpha for wins, beta for losses)
    alpha = 3
    beta = 3
    win_rates['smoothed_win_rate'] = (win_rates['wins'] + alpha) / (win_rates['appearances'] + alpha + beta)
    
    hero_win_rates = win_rates['smoothed_win_rate'].to_dict()
    
    # Get all hero IDs to create a complete matrix
    all_hero_ids = get_hero_ids()
    if not all_hero_ids:
        logging.error("Could not retrieve hero list. Aborting.")
        return
        
    logging.info(f"Found {len(all_hero_ids)} unique heroes. Building counter-rate matrix...")

    # Create N x N matrix
    counter_matrix = pd.DataFrame(index=all_hero_ids, columns=all_hero_ids, dtype=np.float64)

    for hero_a_id in all_hero_ids:
        for hero_b_id in all_hero_ids:
            if hero_a_id == hero_b_id:
                counter_matrix.loc[hero_a_id, hero_b_id] = 0.5  # A hero vs itself is a 50% chance
                continue
            
            pA = hero_win_rates.get(hero_a_id, 0.5) # Default to 0.5 if hero never appeared
            pB = hero_win_rates.get(hero_b_id, 0.5)
            
            counter_matrix.loc[hero_a_id, hero_b_id] = log5(pA, pB)

    # Save the matrix
    OUTPUT_DIR.mkdir(exist_ok=True)
    counter_matrix.to_csv(OUTPUT_PATH)
    
    logging.info(f"Successfully computed counter-rate matrix for {len(all_hero_ids)} heroes.")
    logging.info(f"Matrix saved to {OUTPUT_PATH}")

if __name__ == '__main__':
    main()
