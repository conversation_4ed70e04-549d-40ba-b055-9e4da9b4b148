"""
Compute player-hero performance statistics from historical Kaggle data.

This script processes all available historical data (2023-2025) to calculate:
- Total games played by each player with each hero
- Win rate of each player with each hero

The output is saved as a lookup table for use in the main dataset pipeline.
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Tuple
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define paths
KAGGLE_DATA_DIR = Path("dataset/kaggle_data")
OUTPUT_PATH = Path("models/player_hero_performance.csv")

# All available months for historical analysis
ALL_MONTHS = ["2023", "2024", "202501", "202502", "202503", "202504", "202505", "202506", "202507"]

def load_all_player_data() -> pd.DataFrame:
    """Load player data from all available months."""
    all_dfs = []
    
    for month in ALL_MONTHS:
        month_dir = KAGGLE_DATA_DIR / month
        players_file = month_dir / "players.csv"
        
        if players_file.exists():
            logger.info(f"Loading player data for month: {month}")
            try:
                # Only load the columns we need to save memory
                df = pd.read_csv(players_file, usecols=['account_id', 'hero_id', 'win', 'lose'])
                df['month'] = month  # Track which month this data came from
                all_dfs.append(df)
                logger.info(f"Loaded {len(df)} player records from {month}")
            except Exception as e:
                logger.error(f"Could not read players.csv in {month}: {e}")
        else:
            logger.warning(f"Warning: missing players.csv for month {month}")
    
    if not all_dfs:
        raise ValueError("No player data found in any month!")
    
    combined_df = pd.concat(all_dfs, ignore_index=True)
    logger.info(f"Combined dataset has {len(combined_df)} total player records")
    return combined_df

def calculate_performance(players_df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate win rate and total games for each (account_id, hero_id) combination.
    
    Args:
        players_df: DataFrame with columns ['account_id', 'hero_id', 'win', 'lose']
    
    Returns:
        DataFrame with MultiIndex (account_id, hero_id) and columns ['win_rate', 'total_games']
    """
    logger.info("Calculating player-hero performance statistics...")
    
    # Handle missing account_ids (anonymous players)
    # Replace NaN account_ids with a special value (-1)
    players_df = players_df.copy()
    players_df['account_id'] = players_df['account_id'].fillna(-1).astype(int)
    
    # Validate data integrity
    logger.info("Validating data integrity...")
    
    # Check that win + lose = 1 for each row (should be mutually exclusive)
    win_lose_sum = players_df['win'] + players_df['lose']
    invalid_rows = win_lose_sum != 1.0
    if invalid_rows.any():
        logger.warning(f"Found {invalid_rows.sum()} rows where win + lose != 1. These will be excluded.")
        players_df = players_df[~invalid_rows]
    
    # Group by (account_id, hero_id) and calculate statistics
    logger.info("Grouping by (account_id, hero_id) and calculating statistics...")
    
    grouped = players_df.groupby(['account_id', 'hero_id']).agg({
        'win': ['sum', 'count'],  # sum gives total wins, count gives total games
        'lose': 'sum'  # sum gives total losses (should equal count - win_sum)
    }).reset_index()
    
    # Flatten column names
    grouped.columns = ['account_id', 'hero_id', 'total_wins', 'total_games', 'total_losses']
    
    # Calculate win rate
    grouped['win_rate'] = grouped['total_wins'] / grouped['total_games']
    
    # Data validation
    logger.info("Performing data validation...")
    
    # Check that total_wins + total_losses = total_games
    games_check = grouped['total_wins'] + grouped['total_losses'] == grouped['total_games']
    if not games_check.all():
        logger.error(f"Data integrity error: {(~games_check).sum()} rows where wins + losses != total games")
        raise ValueError("Data integrity check failed")
    
    # Check win rate bounds
    invalid_win_rates = (grouped['win_rate'] < 0) | (grouped['win_rate'] > 1)
    if invalid_win_rates.any():
        logger.error(f"Found {invalid_win_rates.sum()} invalid win rates (outside 0-1 range)")
        raise ValueError("Win rate validation failed")
    
    # Set MultiIndex for efficient lookup
    result = grouped.set_index(['account_id', 'hero_id'])[['win_rate', 'total_games']]
    
    logger.info(f"Calculated performance for {len(result)} unique (player, hero) combinations")
    logger.info(f"Average win rate: {grouped['win_rate'].mean():.4f}")
    logger.info(f"Average games per player-hero combo: {grouped['total_games'].mean():.2f}")
    
    return result

def save_performance_data(performance_df: pd.DataFrame, output_path: Path):
    """Save the performance data to CSV."""
    logger.info(f"Saving performance data to {output_path}")
    
    # Ensure output directory exists
    output_path.parent.mkdir(exist_ok=True)
    
    # Save with explicit index to make loading easier
    performance_df.to_csv(output_path)
    
    logger.info(f"Successfully saved {len(performance_df)} player-hero performance records")

def main():
    """Main function to compute and save player-hero performance statistics."""
    logger.info("Starting player-hero performance computation...")
    
    try:
        # Load all historical player data
        players_df = load_all_player_data()
        
        # Calculate performance statistics
        performance_df = calculate_performance(players_df)
        
        # Save results
        save_performance_data(performance_df, OUTPUT_PATH)
        
        logger.info("Player-hero performance computation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during computation: {e}")
        raise

if __name__ == "__main__":
    main()
