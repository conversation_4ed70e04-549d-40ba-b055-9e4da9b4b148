import pandas as pd
import numpy as np

# Load dataset
df = pd.read_csv('dataset/train_data/sequential_drafts.csv')

print(f"Dataset size: {len(df)} matches")
print("\nClass distribution:")
print(df['radiant_win'].value_counts())
print(f"\nRadiant win rate: {df['radiant_win'].mean():.3f}")

# Check for any patterns
print(f"\nUnique heroes in dataset:")
all_heroes = set()
for _, row in df.iterrows():
    heroes = [int(x.strip()) for x in row['pick_sequence'].split(',')]
    all_heroes.update(heroes)

print(f"Total unique heroes: {len(all_heroes)}")
print(f"Hero ID range: {min(all_heroes)} - {max(all_heroes)}")
