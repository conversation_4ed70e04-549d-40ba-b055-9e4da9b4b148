# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test metrics for draft-phase model v1 to ensure improvement over baseline.
"""

import pytest
import pandas as pd
import numpy as np
import joblib
import logging
import json
import os
from sklearn.metrics import accuracy_score, log_loss, roc_auc_score, brier_score_loss
from sklearn.model_selection import train_test_split
import xgboost as xgb

from dataset.feature_schemas import validate_features_for_draft_prediction
from structure.helpers import prepare_draft_phase_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_baseline_metrics():
    """Load baseline metrics for comparison."""
    baseline_path = "tests/expected/baseline_metrics.json"
    if os.path.exists(baseline_path):
        with open(baseline_path, 'r') as f:
            return json.load(f)
    else:
        # If no baseline exists, create a minimal one
        return {
            "accuracy": 0.50,  # Coin flip baseline
            "log_loss": 0.693,  # -log(0.5) baseline
            "roc_auc": 0.50,
            "brier_score": 0.25
        }

def test_draft_model_v1_metrics():
    """Test that draft model v1 shows improvement over baseline."""
    
    # Load baseline metrics
    baseline = load_baseline_metrics()
    logger.info(f"Baseline metrics: {baseline}")
    
    # Load draft-phase dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Use a deterministic subset for consistent testing
    np.random.seed(42)
    test_size = min(5000, len(df))  # Use up to 5000 samples for testing
    df_test = df.sample(n=test_size, random_state=42)
    
    # Prepare data
    df_prepared = prepare_draft_phase_data(df_test.copy(), "scaler_draft.pkl")
    
    # Split features and target
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    y = df_prepared['radiant_win'].astype(int)
    
    # Validate features
    is_valid, forbidden = validate_features_for_draft_prediction(X.columns.tolist() + ['radiant_win'])
    assert is_valid, f"Test dataset contains forbidden features: {forbidden}"
    
    # Split for testing
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Load the trained draft model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
        logger.info("Loaded draft model v1")
    except FileNotFoundError:
        pytest.skip("Draft model not found - run ml/create_model_draft_phase.py first")
    
    # Make predictions
    dtest = xgb.DMatrix(X_test)
    y_pred_proba = model.predict(dtest)
    y_pred = (y_pred_proba > 0.5).astype(int)
    
    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)
    logloss = log_loss(y_test, y_pred_proba)
    auc = roc_auc_score(y_test, y_pred_proba)
    brier = brier_score_loss(y_test, y_pred_proba)
    
    logger.info(f"Draft Model v1 Performance:")
    logger.info(f"Accuracy: {accuracy:.4f}")
    logger.info(f"Log Loss: {logloss:.4f}")
    logger.info(f"ROC AUC: {auc:.4f}")
    logger.info(f"Brier Score: {brier:.4f}")
    
    # Save current metrics
    current_metrics = {
        "accuracy": accuracy,
        "log_loss": logloss,
        "roc_auc": auc,
        "brier_score": brier,
        "model_type": "draft_model_v1",
        "dataset_size": len(y_test),
        "feature_count": len(X_test.columns)
    }
    
    os.makedirs("tests/expected", exist_ok=True)
    with open("tests/expected/draft_v1_metrics.json", "w") as f:
        json.dump(current_metrics, f, indent=2)
    
    # Test assertions - require improvement over baseline
    baseline_accuracy = baseline.get("accuracy", 0.50)
    baseline_logloss = baseline.get("log_loss", 0.693)
    
    # Allow for some flexibility in baseline comparison
    if "error" in baseline:
        # If baseline failed, just ensure we're better than coin flip
        assert accuracy > 0.50, f"Accuracy {accuracy:.4f} not better than coin flip"
        assert logloss < 0.693, f"Log loss {logloss:.4f} not better than random baseline"
    else:
        # Compare against actual baseline with some tolerance
        min_accuracy_improvement = 0.01  # At least 1% improvement
        max_logloss_increase = 0.02      # Allow small increase in log loss
        
        assert accuracy >= baseline_accuracy + min_accuracy_improvement, \
            f"Accuracy {accuracy:.4f} not sufficiently better than baseline {baseline_accuracy:.4f}"
        
        # For log loss, lower is better, so we want current <= baseline + tolerance
        if baseline_logloss < 1.0:  # Only if baseline log loss is reasonable
            assert logloss <= baseline_logloss + max_logloss_increase, \
                f"Log loss {logloss:.4f} worse than baseline {baseline_logloss:.4f}"
    
    # General sanity checks
    assert 0.50 < accuracy < 0.95, f"Accuracy {accuracy:.4f} outside reasonable range"
    assert 0.4 < logloss < 1.0, f"Log loss {logloss:.4f} outside reasonable range"
    assert 0.50 < auc < 0.95, f"AUC {auc:.4f} outside reasonable range"
    
    logger.info("✅ Draft model v1 metrics test passed")

def test_no_forbidden_features():
    """Test that the model uses no forbidden post-match features."""
    
    # Load the draft dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Validate all columns
    is_valid, forbidden = validate_features_for_draft_prediction(df.columns.tolist())
    assert is_valid, f"Draft dataset contains forbidden features: {forbidden}"
    
    # Prepare data and validate again
    df_prepared = prepare_draft_phase_data(df.head(100).copy(), "scaler_draft.pkl")
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    
    is_valid, forbidden = validate_features_for_draft_prediction(X.columns.tolist() + ['radiant_win'])
    assert is_valid, f"Prepared features contain forbidden elements: {forbidden}"
    
    logger.info("✅ No forbidden features test passed")

def test_feature_count_stability():
    """Test that the model uses a stable number of features."""
    
    # Load draft dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    df_prepared = prepare_draft_phase_data(df.head(100).copy(), "scaler_draft.pkl")
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    
    # Should have exactly 10 hero ID features for basic draft model
    expected_features = 10
    actual_features = len(X.columns)
    
    assert actual_features >= expected_features, \
        f"Too few features: {actual_features}, expected at least {expected_features}"
    
    # Check that we have the expected hero ID features
    hero_id_features = [col for col in X.columns if 'hero_id' in col]
    assert len(hero_id_features) == 10, \
        f"Expected 10 hero ID features, got {len(hero_id_features)}: {hero_id_features}"
    
    logger.info(f"✅ Feature count test passed: {actual_features} features")

if __name__ == "__main__":
    # Run tests directly
    test_no_forbidden_features()
    test_feature_count_stability()
    test_draft_model_v1_metrics()
