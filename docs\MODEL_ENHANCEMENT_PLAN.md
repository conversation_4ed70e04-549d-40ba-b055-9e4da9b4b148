# Dota 2 LSTM Model Enhancement Plan: Towards a "Star of the Show" Predictor

This document outlines a multi-tiered plan to significantly enhance the existing LSTM-based Dota 2 draft prediction model. The goal is to move beyond the foundational implementation of the reference paper (`FULLTEXT01.txt`) by incorporating richer pre-game data, thereby creating a more robust, accurate, and insightful predictive tool.

## Current Model Status (Paper Alignment)

The current model successfully implements the core methodologies from `FULLTEXT01.txt`:
-   **CBOW for Hero Embeddings:** Generates hero vectors based on pick patterns.
-   **Team-Specific Draft Sequences:** Processes separate 5-hero pick sequences for Radiant and Dire.
-   **Log5 Counter-Rates:** Incorporates hero-vs-hero win probabilities based on the Log5 formula.
-   **Aggregated Static Hero Features:** Includes counts of roles, primary attributes, and attack types for each team's draft (an enhancement over the paper).

## Enhancement Tiers

### Tier 1: The "Paper++" Model (Essential & Direct Enhancements)

This tier focuses on the most impactful and readily available features that directly address limitations acknowledged by the original paper or represent logical extensions.

#### Feature 1.1: Player-Specific Hero Performance
-   **Description:** For each of the 10 players in a match, calculate their historical win rate and total games played with the specific hero they picked. This captures individual player proficiency with a hero.
-   **Data Source:** `dataset/kaggle_data/<year>/players.csv` (specifically `account_id`, `hero_id`, `win`, `lose`).
-   **Implementation Steps:**
    1.  **Create `scripts/compute_player_hero_performance.py`:**
        *   This script will load `players.csv` across all historical months (2023-2025).
        *   For each unique `(account_id, hero_id)` pair, it will compute:
            *   `total_games`: Total matches played by this player with this hero.
            *   `win_rate`: Historical win rate of this player with this hero (wins / total_games).
        *   It should handle cases where `account_id` is missing or `-1` (unknown players) by assigning a default/average win rate (e.g., 0.5) and 0 games.
        *   Save the results to `models/player_hero_performance.csv` (or a similar persistent format for lookup).
    2.  **Modify `scripts/create_sequential_dataset_from_kaggle.py`:**
        *   Load `models/player_hero_performance.csv` as a lookup table.
        *   For each of the 10 picks in a match (5 Radiant, 5 Dire):
            *   Retrieve the `account_id` (from `players.csv` for that match) and `hero_id`.
            *   Look up the `win_rate` and `total_games` for that `(account_id, hero_id)` pair.
            *   If no data is found (e.g., new player/hero combo), use default values (e.g., 0.5 win rate, 0 games).
        *   Aggregate these 5 player-hero stats for each team (Radiant and Dire) into new columns in `sequential_drafts_with_features.csv`. Possible aggregation methods: `avg_win_rate`, `sum_total_games`, `min_win_rate`, `max_win_rate`. Start with `avg_win_rate` and `sum_total_games`.
        *   New columns: `radiant_avg_player_hero_win_rate`, `radiant_sum_player_hero_games`, `dire_avg_player_hero_win_rate`, `dire_sum_player_hero_games`.
    3.  **Update `ml/create_model_lstm.py`:**
        *   Modify the model architecture to accept these 4 new numerical features as additional inputs (e.g., via a small dense layer concatenated with the main LSTM output).
-   **Justification:** Directly addresses a limitation of the paper, providing crucial context on player proficiency.

#### Feature 1.2: Team Head-to-Head & Global Win Rate
-   **Description:** Incorporate historical performance metrics for the specific teams involved in a match.
-   **Data Source:** `dataset/kaggle_data/<year>/teams.csv` (for `team_id`, `name`) and `dataset/kaggle_data/<year>/main_metadata.csv` (for `radiant_team_id`, `dire_team_id`, `radiant_win`).
-   **Implementation Steps:**
    1.  **Create `scripts/compute_team_performance.py`:**
        *   Load `main_metadata.csv` across all historical months (2023-2025).
        *   For each unique `(team_id_A, team_id_B)` pair, compute `h2h_win_rate_A_vs_B`.
        *   For each unique `team_id`, compute `global_win_rate`.
        *   Handle unknown team IDs (`-1` or missing) by assigning default/average win rates.
        *   Save results to `models/team_performance.csv`.
    2.  **Modify `scripts/create_sequential_dataset_from_kaggle.py`:**
        *   Load `models/team_performance.csv` as a lookup table.
        *   For each match, retrieve `radiant_team_id` and `dire_team_id`.
        *   Look up `h2h_win_rate` (Radiant vs. Dire), `radiant_global_win_rate`, and `dire_global_win_rate`.
        *   New columns: `h2h_radiant_win_rate`, `radiant_global_win_rate`, `dire_global_win_rate`.
    3.  **Update `ml/create_model_lstm.py`:**
        *   Modify the model architecture to accept these 3 new numerical features as additional inputs.
-   **Justification:** Adds a critical layer of team-level context, moving beyond hero-only analysis.

### Tier 2: The "Meta-Aware" Model (Advanced Contextual Features)

This tier focuses on features that provide the model with a deeper understanding of the game's evolving environment.

#### Feature 2.1: Patch Version
-   **Description:** Identify the specific game patch a match was played on.
-   **Data Source:** `dataset/kaggle_data/Constants/Constants.Patch.csv` (for `patch`, `date`) and `main_metadata.csv` (for `start_date_time`).
-   **Implementation Steps:**
    1.  **Modify `scripts/create_sequential_dataset_from_kaggle.py`:**
        *   Load `Constants.Patch.csv`.
        *   For each match, determine the active patch based on `start_date_time`.
        *   Add a `patch_version` column (e.g., '7.15', '7.16').
    2.  **Update `ml/create_model_lstm.py`:**
        *   Convert `patch_version` into a numerical representation (e.g., one-hot encoding or ordinal encoding if a clear progression is desired).
        *   Integrate this into the model architecture.
-   **Justification:** Dota 2 is heavily influenced by patches. This allows the model to learn patch-specific meta shifts.

#### Feature 2.2: Tournament Tier
-   **Description:** Categorize the importance or competitive level of a match.
-   **Data Source:** `dataset/kaggle_data/Constants/Constants.Leagues.csv` (for `leagueid`, `tier`) and `main_metadata.csv` (for `leagueid`).
-   **Implementation Steps:**
    1.  **Modify `scripts/create_sequential_dataset_from_kaggle.py`:**
        *   Load `Constants.Leagues.csv`.
        *   For each match, map `leagueid` to its `tier` (e.g., 'professional', 'premium', 'amateur').
        *   Add a `tournament_tier` column.
    2.  **Update `ml/create_model_lstm.py`:**
        *   Convert `tournament_tier` into a numerical representation (e.g., one-hot encoding).
        *   Integrate this into the model architecture.
-   **Justification:** Match importance can influence player behavior and draft strategies.

### Tier 3: The "Draft Strategy" Model (Experimental & Complex Features)

This tier explores more complex features related to the strategic choices made during the draft.

#### Feature 3.1: Ban Phase Integration
-   **Description:** Incorporate information about banned heroes into the model.
-   **Data Source:** `dataset/kaggle_data/<year>/picks_bans.csv` (specifically `is_pick == False`).
-   **Implementation Steps:**
    1.  **Modify `scripts/create_sequential_dataset_from_kaggle.py`:**
        *   For each match, extract the 10 banned heroes (5 Radiant, 5 Dire).
        *   Create two new columns: `radiant_bans_sequence` and `dire_bans_sequence`.
    2.  **Update `ml/create_model_lstm.py`:**
        *   This is a significant architectural change. It would likely involve:
            *   Creating separate embedding layers for banned heroes (or using the same hero embeddings).
            *   Processing ban sequences through additional LSTM layers or simpler dense layers.
            *   Concatenating these ban features with the existing pick features before the final classification head.
-   **Justification:** Bans are a strong signal of strategic intent and hero power.

## Implementation Workflow for AI Coder Agent

The AI Coder Agent should follow these steps for each feature, in the order presented (Tier 1 -> Tier 2 -> Tier 3):

1.  **Understand Feature:** Read the feature description and justification.
2.  **Identify Data Sources:** Confirm the necessary columns in the specified CSVs.
3.  **Develop/Modify Script:**
    *   If a new script is needed (e.g., `compute_player_hero_performance.py`), create it.
    *   If an existing script needs modification (e.g., `create_sequential_dataset_from_kaggle.py`), apply the changes.
    *   Ensure proper logging and error handling.
4.  **Execute Script:** Run the data generation/processing scripts to create/update the necessary data artifacts.
5.  **Modify Model Architecture:** Update `ml/create_model_lstm.py` to accept and process the new features. This will involve:
    *   Adding new input tensors to the model.
    *   Defining appropriate layers (e.g., `nn.Embedding`, `nn.Linear`, `nn.LSTM`) to process these inputs.
    *   Concatenating or combining these processed features with the existing model outputs.
6.  **Update Documentation:** Modify `docs/LSTM_MODEL_DOCUMENTATION.md` and `docs/LSTM_IMPLEMENTATION_PLAN.md` to reflect the new features, data sources, and architectural changes.
7.  **Test & Validate:** Run relevant unit tests and perform a quick training run to ensure the new features are integrated without breaking the pipeline.

**Important Considerations for the AI Coder Agent:**
-   **Incremental Development:** Implement one feature at a time, fully testing and documenting before moving to the next.
-   **Error Handling:** Be prepared to debug `FileNotFoundError`, `KeyError` (due to missing columns or data), and `MemoryError` during data processing or model training.
-   **Data Volume:** Remember that `compute_player_hero_performance.py` and `compute_team_performance.py` should ideally use *all historical data* (2023-2025) to build robust statistics, while the main `create_sequential_dataset_from_kaggle.py` should use only the most recent data (2025) for the LSTM training set.
-   **Model Complexity:** As more features are added, the model architecture in `ml/create_model_lstm.py` will become more complex. Pay attention to input shapes and tensor concatenations.
-   **Performance:** Monitor training time and memory usage with each new feature.

This plan provides a clear roadmap for building a highly advanced Dota 2 prediction model.
