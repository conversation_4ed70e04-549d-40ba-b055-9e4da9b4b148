# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.
# DEPRECATED: This script is obsolete. Use scripts/create_sequential_dataset_from_kaggle.py instead.

"""
Generate sequential draft dataset for LSTM model training.
Extracts chronological pick sequences from OpenDota API for each match.
"""

import pandas as pd
import requests
import logging
import time
import os
import sys
from typing import List, Dict, Optional, Tuple

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import opendota_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def rate_limit_decorator(calls_per_second: float = 1.0):
    """Rate limiting decorator for API calls."""
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            result = func(*args, **kwargs)
            last_called[0] = time.time()
            return result
        return wrapper
    return decorator

@rate_limit_decorator(calls_per_second=2.0)  # OpenDota rate limit (increased for registered users)
def fetch_match_picks_bans(match_id: int) -> Optional[Dict]:
    """
    Fetch match data from OpenDota API and extract picks_bans information.
    
    Args:
        match_id: The match ID to fetch
        
    Returns:
        Match data dictionary or None if failed
    """
    try:
        url = f"https://api.opendota.com/api/matches/{match_id}"
        params = {}
        if opendota_key:
            params['api_key'] = opendota_key
            
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        match_data = response.json()
        logger.debug(f"Successfully fetched match {match_id}")
        return match_data
        
    except requests.exceptions.RequestException as e:
        logger.warning(f"Failed to fetch match {match_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error fetching match {match_id}: {e}")
        return None

def extract_pick_sequence(match_data: Dict) -> Optional[Tuple[List[int], bool]]:
    """
    Extract chronological pick sequence from match data.
    
    Args:
        match_data: Match data from OpenDota API
        
    Returns:
        Tuple of (pick_sequence, radiant_win) or None if extraction failed
    """
    try:
        picks_bans = match_data.get('picks_bans', [])
        if not picks_bans:
            logger.warning(f"No picks_bans data for match {match_data.get('match_id', 'unknown')}")
            return None
        
        # Filter for picks only (is_pick=True) and sort by order
        picks = [pb for pb in picks_bans if pb.get('is_pick', False)]
        picks.sort(key=lambda x: x.get('order', 0))
        
        # Extract hero IDs in chronological order
        pick_sequence = [pick.get('hero_id') for pick in picks if pick.get('hero_id')]
        
        # Validate we have exactly 10 picks
        if len(pick_sequence) != 10:
            logger.warning(f"Invalid pick count for match {match_data.get('match_id')}: {len(pick_sequence)} picks")
            return None
        
        # Get match result
        radiant_win = match_data.get('radiant_win', False)
        
        return pick_sequence, radiant_win
        
    except Exception as e:
        logger.error(f"Error extracting pick sequence: {e}")
        return None

def load_existing_matches() -> pd.DataFrame:
    """Load existing draft phase dataset to get match IDs."""
    try:
        df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
        logger.info(f"Loaded {len(df)} matches from existing draft phase dataset")
        return df
    except FileNotFoundError:
        logger.error("Draft phase dataset not found. Please run generate_dataset_draft_phase.py first")
        raise
    except Exception as e:
        logger.error(f"Error loading draft phase dataset: {e}")
        raise

def generate_sequential_draft_dataset(batch_size: int = 1000):
    """
    Generate sequential draft dataset from existing match IDs.
    Creates dataset/train_data/sequential_drafts.csv with pick sequences.

    Args:
        batch_size: Number of matches to process in this batch (0 = process all)
    """
    logger.info(f"Starting sequential draft dataset generation (batch_size={batch_size})")
    
    # Load existing matches
    existing_df = load_existing_matches()
    match_ids = existing_df['match_id'].unique()
    logger.info(f"Processing {len(match_ids)} unique matches")
    
    # Check if output file already exists
    output_path = "dataset/train_data/sequential_drafts.csv"
    if os.path.exists(output_path):
        logger.info(f"Output file {output_path} already exists. Loading existing data...")
        try:
            existing_sequential = pd.read_csv(output_path)
            processed_matches = set(existing_sequential['match_id'].tolist())
            logger.info(f"Found {len(processed_matches)} already processed matches")
        except Exception as e:
            logger.warning(f"Error loading existing file: {e}. Starting fresh.")
            processed_matches = set()
    else:
        processed_matches = set()
    
    # Filter out already processed matches
    remaining_matches = [mid for mid in match_ids if mid not in processed_matches]

    # Limit batch size if specified
    if batch_size > 0 and len(remaining_matches) > batch_size:
        remaining_matches = remaining_matches[:batch_size]
        logger.info(f"Processing batch of {len(remaining_matches)} matches (limited by batch_size)")

    logger.info(f"Processing {len(remaining_matches)} remaining matches out of {len(match_ids)} total matches")
    
    sequential_data = []
    failed_matches = []
    
    for i, match_id in enumerate(remaining_matches):
        if i % 100 == 0:
            logger.info(f"Progress: {i}/{len(remaining_matches)} matches processed")
        
        # Fetch match data
        match_data = fetch_match_picks_bans(match_id)
        if match_data is None:
            failed_matches.append(match_id)
            continue
        
        # Extract pick sequence
        result = extract_pick_sequence(match_data)
        if result is None:
            failed_matches.append(match_id)
            continue
        
        pick_sequence, radiant_win = result
        
        # Create row for dataset
        row = {
            'match_id': match_id,
            'pick_sequence': ','.join(map(str, pick_sequence)),
            'radiant_win': int(radiant_win)
        }
        sequential_data.append(row)
        
        # Save progress every 500 matches
        if len(sequential_data) % 500 == 0:
            save_progress(sequential_data, output_path, processed_matches)
    
    # Final save
    if sequential_data:
        save_progress(sequential_data, output_path, processed_matches)
    
    logger.info(f"Sequential draft dataset generation completed")
    logger.info(f"Successfully processed: {len(sequential_data)} matches")
    logger.info(f"Failed matches: {len(failed_matches)}")
    
    if failed_matches:
        logger.info(f"First 10 failed match IDs: {failed_matches[:10]}")

def save_progress(new_data: List[Dict], output_path: str, processed_matches: set):
    """Save progress to CSV file."""
    try:
        # Load existing data if file exists
        if os.path.exists(output_path):
            existing_df = pd.read_csv(output_path)
            all_data = existing_df.to_dict('records') + new_data
        else:
            all_data = new_data
        
        # Create DataFrame and save
        df = pd.DataFrame(all_data)
        df.drop_duplicates(subset=['match_id'], inplace=True)
        df.to_csv(output_path, index=False)
        
        logger.info(f"Saved progress: {len(df)} total matches in {output_path}")
        
    except Exception as e:
        logger.error(f"Error saving progress: {e}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generate sequential draft dataset")
    parser.add_argument("--batch-size", type=int, default=1000,
                       help="Number of matches to process (0 = all remaining)")
    args = parser.parse_args()

    try:
        generate_sequential_draft_dataset(batch_size=args.batch_size)
        logger.info("🎉 Sequential draft dataset generation completed successfully!")
    except Exception as e:
        logger.error(f"❌ Generation failed: {e}")
        raise
