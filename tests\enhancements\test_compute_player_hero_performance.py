"""
Unit tests for compute_player_hero_performance.py

Tests the logic for calculating player-hero performance statistics.
"""

import pandas as pd
import pytest
import numpy as np
import sys
from pathlib import Path

# Add scripts directory to path so we can import the module
sys.path.append(str(Path(__file__).parent.parent.parent / "scripts"))

from compute_player_hero_performance import calculate_performance

class TestCalculatePerformance:
    """Test the calculate_performance function with various scenarios."""
    
    def test_basic_performance_calculation(self):
        """Test basic win rate and total games calculation."""
        # Arrange: Create mock input data
        mock_data = pd.DataFrame({
            'account_id': [101, 101, 102, 102, 103],
            'hero_id': [5, 5, 10, 10, 5],
            'win': [1.0, 0.0, 1.0, 1.0, 0.0],
            'lose': [0.0, 1.0, 0.0, 0.0, 1.0]
        })
        
        # Act: Run the function
        result_df = calculate_performance(mock_data)
        
        # Assert: Check results
        # Player 101 with hero 5: 1 win, 1 loss -> 0.5 win rate, 2 games
        player101_hero5 = result_df.loc[(101, 5)]
        assert player101_hero5['win_rate'] == 0.5
        assert player101_hero5['total_games'] == 2
        
        # Player 102 with hero 10: 2 wins, 0 losses -> 1.0 win rate, 2 games
        player102_hero10 = result_df.loc[(102, 10)]
        assert player102_hero10['win_rate'] == 1.0
        assert player102_hero10['total_games'] == 2
        
        # Player 103 with hero 5: 0 wins, 1 loss -> 0.0 win rate, 1 game
        player103_hero5 = result_df.loc[(103, 5)]
        assert player103_hero5['win_rate'] == 0.0
        assert player103_hero5['total_games'] == 1
    
    def test_missing_account_id_handling(self):
        """Test that missing account_ids are handled correctly."""
        # Arrange: Create data with NaN account_id
        mock_data = pd.DataFrame({
            'account_id': [101, np.nan, np.nan],
            'hero_id': [5, 10, 10],
            'win': [1.0, 1.0, 0.0],
            'lose': [0.0, 0.0, 1.0]
        })
        
        # Act
        result_df = calculate_performance(mock_data)
        
        # Assert: NaN account_ids should be converted to -1
        assert (101, 5) in result_df.index
        assert (-1, 10) in result_df.index
        
        # The anonymous player (-1) with hero 10 should have 1 win, 1 loss
        anonymous_hero10 = result_df.loc[(-1, 10)]
        assert anonymous_hero10['win_rate'] == 0.5
        assert anonymous_hero10['total_games'] == 2
    
    def test_single_game_scenarios(self):
        """Test edge cases with single games."""
        # Arrange: Single win and single loss
        mock_data = pd.DataFrame({
            'account_id': [201, 202],
            'hero_id': [15, 20],
            'win': [1.0, 0.0],
            'lose': [0.0, 1.0]
        })
        
        # Act
        result_df = calculate_performance(mock_data)
        
        # Assert
        player201_hero15 = result_df.loc[(201, 15)]
        assert player201_hero15['win_rate'] == 1.0
        assert player201_hero15['total_games'] == 1
        
        player202_hero20 = result_df.loc[(202, 20)]
        assert player202_hero20['win_rate'] == 0.0
        assert player202_hero20['total_games'] == 1
    
    def test_data_integrity_validation(self):
        """Test that invalid data raises appropriate errors."""
        # Arrange: Create data where win + lose != 1
        invalid_data = pd.DataFrame({
            'account_id': [301],
            'hero_id': [25],
            'win': [1.0],
            'lose': [1.0]  # Both win and lose are 1 - invalid!
        })
        
        # Act & Assert: Should handle this by filtering out invalid rows
        # The function should log a warning but not crash
        result_df = calculate_performance(invalid_data)
        
        # Should result in empty DataFrame since all rows were invalid
        assert len(result_df) == 0
    
    def test_multiple_heroes_same_player(self):
        """Test a player with multiple different heroes."""
        # Arrange
        mock_data = pd.DataFrame({
            'account_id': [401, 401, 401, 401, 401],
            'hero_id': [30, 30, 31, 31, 32],
            'win': [1.0, 1.0, 0.0, 1.0, 1.0],
            'lose': [0.0, 0.0, 1.0, 0.0, 0.0]
        })
        
        # Act
        result_df = calculate_performance(mock_data)
        
        # Assert
        # Hero 30: 2 wins, 0 losses
        player401_hero30 = result_df.loc[(401, 30)]
        assert player401_hero30['win_rate'] == 1.0
        assert player401_hero30['total_games'] == 2
        
        # Hero 31: 1 win, 1 loss
        player401_hero31 = result_df.loc[(401, 31)]
        assert player401_hero31['win_rate'] == 0.5
        assert player401_hero31['total_games'] == 2
        
        # Hero 32: 1 win, 0 losses
        player401_hero32 = result_df.loc[(401, 32)]
        assert player401_hero32['win_rate'] == 1.0
        assert player401_hero32['total_games'] == 1
    
    def test_result_dataframe_structure(self):
        """Test that the result DataFrame has the correct structure."""
        # Arrange
        mock_data = pd.DataFrame({
            'account_id': [501, 502],
            'hero_id': [40, 41],
            'win': [1.0, 0.0],
            'lose': [0.0, 1.0]
        })
        
        # Act
        result_df = calculate_performance(mock_data)
        
        # Assert structure
        assert isinstance(result_df.index, pd.MultiIndex)
        assert result_df.index.names == ['account_id', 'hero_id']
        assert list(result_df.columns) == ['win_rate', 'total_games']
        assert len(result_df) == 2
        
        # Check data types
        assert result_df['win_rate'].dtype == float
        assert result_df['total_games'].dtype in [int, np.int64]
