# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Production model deployment and monitoring framework.
Handles model versioning, deployment, and performance monitoring.
"""

import pandas as pd
import numpy as np
import logging
import json
import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import xgboost as xgb

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionModelManager:
    """Manages production model deployment and monitoring."""
    
    def __init__(self, production_dir="production/models"):
        self.production_dir = Path(production_dir)
        self.production_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_model_path = self.production_dir / "current_model.pkl"
        self.model_config_path = self.production_dir / "model_config.json"
        self.performance_log_path = self.production_dir / "performance_log.json"
        
    def deploy_model(self, model_path, version_name, metadata=None):
        """Deploy a new model to production."""
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found: {model_path}")
        
        # Backup current model if it exists
        if self.current_model_path.exists():
            backup_path = self.production_dir / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            shutil.copy2(self.current_model_path, backup_path)
            logger.info(f"Backed up current model to {backup_path}")
        
        # Deploy new model
        shutil.copy2(model_path, self.current_model_path)
        
        # Update configuration
        config = {
            "version_name": version_name,
            "deployed_at": datetime.now().isoformat(),
            "model_path": str(model_path),
            "metadata": metadata or {},
            "status": "active"
        }
        
        with open(self.model_config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"✅ Deployed model {version_name} to production")
        return config
    
    def get_current_model_info(self):
        """Get information about the currently deployed model."""
        
        if not self.model_config_path.exists():
            return None
        
        with open(self.model_config_path, 'r') as f:
            return json.load(f)
    
    def load_current_model(self):
        """Load the currently deployed model."""
        
        if not self.current_model_path.exists():
            raise FileNotFoundError("No model currently deployed")
        
        model = xgb.Booster()
        model.load_model(str(self.current_model_path))
        return model
    
    def log_prediction_performance(self, predictions, actuals, metadata=None):
        """Log prediction performance for monitoring."""
        
        from sklearn.metrics import accuracy_score, log_loss, roc_auc_score, brier_score_loss
        
        # Calculate metrics
        accuracy = accuracy_score(actuals, (predictions > 0.5).astype(int))
        logloss = log_loss(actuals, predictions)
        auc = roc_auc_score(actuals, predictions)
        brier = brier_score_loss(actuals, predictions)
        
        # Create performance entry
        performance_entry = {
            "timestamp": datetime.now().isoformat(),
            "sample_size": len(predictions),
            "metrics": {
                "accuracy": accuracy,
                "log_loss": logloss,
                "roc_auc": auc,
                "brier_score": brier
            },
            "metadata": metadata or {}
        }
        
        # Load existing log
        if self.performance_log_path.exists():
            with open(self.performance_log_path, 'r') as f:
                performance_log = json.load(f)
        else:
            performance_log = {"entries": []}
        
        # Add new entry
        performance_log["entries"].append(performance_entry)
        
        # Keep only last 1000 entries
        if len(performance_log["entries"]) > 1000:
            performance_log["entries"] = performance_log["entries"][-1000:]
        
        # Save updated log
        with open(self.performance_log_path, 'w') as f:
            json.dump(performance_log, f, indent=2)
        
        logger.info(f"Logged performance: acc={accuracy:.4f}, ll={logloss:.4f}")
        return performance_entry
    
    def check_performance_drift(self, lookback_days=7, drift_threshold=0.02):
        """Check for performance drift in recent predictions."""
        
        if not self.performance_log_path.exists():
            return {"drift_detected": False, "reason": "No performance log found"}
        
        with open(self.performance_log_path, 'r') as f:
            performance_log = json.load(f)
        
        entries = performance_log.get("entries", [])
        if len(entries) < 2:
            return {"drift_detected": False, "reason": "Insufficient data"}
        
        # Filter recent entries
        cutoff_date = datetime.now() - timedelta(days=lookback_days)
        recent_entries = []
        
        for entry in entries:
            entry_date = datetime.fromisoformat(entry["timestamp"])
            if entry_date >= cutoff_date:
                recent_entries.append(entry)
        
        if len(recent_entries) < 2:
            return {"drift_detected": False, "reason": "Insufficient recent data"}
        
        # Calculate recent vs historical performance
        recent_accuracy = np.mean([e["metrics"]["accuracy"] for e in recent_entries[-10:]])
        historical_accuracy = np.mean([e["metrics"]["accuracy"] for e in entries[:-10]]) if len(entries) > 10 else recent_accuracy
        
        accuracy_drift = abs(recent_accuracy - historical_accuracy)
        drift_detected = accuracy_drift > drift_threshold
        
        drift_analysis = {
            "drift_detected": drift_detected,
            "accuracy_drift": accuracy_drift,
            "recent_accuracy": recent_accuracy,
            "historical_accuracy": historical_accuracy,
            "threshold": drift_threshold,
            "recent_samples": len(recent_entries),
            "recommendation": "Retrain model" if drift_detected else "Continue monitoring"
        }
        
        if drift_detected:
            logger.warning(f"⚠️ Performance drift detected: {accuracy_drift:.4f} > {drift_threshold}")
        
        return drift_analysis
    
    def schedule_retrain_check(self, last_retrain_date, retrain_interval_days=90):
        """Check if model needs retraining based on schedule."""
        
        if isinstance(last_retrain_date, str):
            last_retrain_date = datetime.fromisoformat(last_retrain_date)
        
        days_since_retrain = (datetime.now() - last_retrain_date).days
        needs_retrain = days_since_retrain >= retrain_interval_days
        
        return {
            "needs_retrain": needs_retrain,
            "days_since_retrain": days_since_retrain,
            "retrain_interval": retrain_interval_days,
            "next_retrain_date": (last_retrain_date + timedelta(days=retrain_interval_days)).isoformat(),
            "reason": "Scheduled quarterly retrain" if needs_retrain else "Within retrain interval"
        }

class ModelPerformanceMonitor:
    """Monitors model performance and alerts on issues."""
    
    def __init__(self, model_manager):
        self.model_manager = model_manager
    
    def daily_health_check(self):
        """Perform daily model health check."""
        
        logger.info("🔍 Performing daily model health check...")
        
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "model_info": self.model_manager.get_current_model_info(),
            "drift_check": self.model_manager.check_performance_drift(),
            "status": "healthy"
        }
        
        # Check if model exists
        if not health_report["model_info"]:
            health_report["status"] = "error"
            health_report["issues"] = ["No model deployed"]
            logger.error("❌ No model currently deployed")
            return health_report
        
        # Check for drift
        if health_report["drift_check"]["drift_detected"]:
            health_report["status"] = "warning"
            health_report["issues"] = ["Performance drift detected"]
            logger.warning("⚠️ Performance drift detected")
        
        # Check retrain schedule
        model_info = health_report["model_info"]
        if "deployed_at" in model_info:
            retrain_check = self.model_manager.schedule_retrain_check(
                model_info["deployed_at"]
            )
            health_report["retrain_check"] = retrain_check
            
            if retrain_check["needs_retrain"]:
                if health_report["status"] == "healthy":
                    health_report["status"] = "warning"
                    health_report["issues"] = []
                health_report["issues"].append("Scheduled retrain due")
                logger.warning("⚠️ Scheduled retrain is due")
        
        if health_report["status"] == "healthy":
            logger.info("✅ Model health check passed")
        
        return health_report
    
    def generate_performance_report(self, days=30):
        """Generate performance report for the last N days."""
        
        if not self.model_manager.performance_log_path.exists():
            return {"error": "No performance data available"}
        
        with open(self.model_manager.performance_log_path, 'r') as f:
            performance_log = json.load(f)
        
        entries = performance_log.get("entries", [])
        
        # Filter entries from last N days
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_entries = []
        
        for entry in entries:
            entry_date = datetime.fromisoformat(entry["timestamp"])
            if entry_date >= cutoff_date:
                recent_entries.append(entry)
        
        if not recent_entries:
            return {"error": f"No performance data in last {days} days"}
        
        # Calculate statistics
        accuracies = [e["metrics"]["accuracy"] for e in recent_entries]
        log_losses = [e["metrics"]["log_loss"] for e in recent_entries]
        aucs = [e["metrics"]["roc_auc"] for e in recent_entries]
        
        report = {
            "period_days": days,
            "total_predictions": sum(e["sample_size"] for e in recent_entries),
            "prediction_sessions": len(recent_entries),
            "performance_summary": {
                "accuracy": {
                    "mean": np.mean(accuracies),
                    "std": np.std(accuracies),
                    "min": np.min(accuracies),
                    "max": np.max(accuracies)
                },
                "log_loss": {
                    "mean": np.mean(log_losses),
                    "std": np.std(log_losses),
                    "min": np.min(log_losses),
                    "max": np.max(log_losses)
                },
                "roc_auc": {
                    "mean": np.mean(aucs),
                    "std": np.std(aucs),
                    "min": np.min(aucs),
                    "max": np.max(aucs)
                }
            },
            "trend_analysis": self._analyze_trends(recent_entries)
        }
        
        return report
    
    def _analyze_trends(self, entries):
        """Analyze performance trends."""
        
        if len(entries) < 5:
            return {"trend": "insufficient_data"}
        
        # Simple trend analysis using linear regression on accuracy
        accuracies = [e["metrics"]["accuracy"] for e in entries]
        x = np.arange(len(accuracies))
        
        # Calculate trend slope
        slope = np.polyfit(x, accuracies, 1)[0]
        
        if slope > 0.001:
            trend = "improving"
        elif slope < -0.001:
            trend = "declining"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "slope": slope,
            "recent_accuracy": np.mean(accuracies[-5:]),
            "early_accuracy": np.mean(accuracies[:5])
        }

if __name__ == "__main__":
    # Demonstrate the production framework
    logger.info("🚀 Production Model Management Framework")
    logger.info("=" * 50)
    
    # Initialize managers
    prod_manager = ProductionModelManager()
    monitor = ModelPerformanceMonitor(prod_manager)
    
    # Show current status
    current_info = prod_manager.get_current_model_info()
    if current_info:
        logger.info(f"Current model: {current_info['version_name']}")
        logger.info(f"Deployed: {current_info['deployed_at']}")
    else:
        logger.info("No model currently deployed")
    
    # Perform health check
    health_report = monitor.daily_health_check()
    logger.info(f"Health status: {health_report['status']}")
    
    logger.info("\n✅ Production framework ready for deployment")
