# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test LSTM model inference endpoint functionality.
Tests the LSTM model's ability to make predictions on sample pick sequences.
"""

import pytest
import torch
import numpy as np
import logging
import os
import sys
from typing import List, <PERSON><PERSON>

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ml.create_model_lstm import CBOWLSTMModel
from gensim.models import KeyedVectors

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LSTMPredictor:
    """LSTM model predictor for draft sequences."""
    
    def __init__(self, model_path: str = "models/lstm_model.pth", 
                 embeddings_path: str = "models/hero_embeddings.kv"):
        self.model = self._load_model(model_path)
        self.embeddings = self._load_embeddings(embeddings_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
    
    def _load_model(self, model_path: str) -> CBOWLSTMModel:
        """Load the trained LSTM model."""
        checkpoint = torch.load(model_path, map_location='cpu')
        model = CBOWLSTMModel(
            embedding_dim=150,
            lstm_hidden_size=128,
            dropout_rate=0.15
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        return model
    
    def _load_embeddings(self, embeddings_path: str) -> KeyedVectors:
        """Load hero embeddings."""
        return KeyedVectors.load(embeddings_path)
    
    def predict(self, pick_sequence: List[int]) -> Tuple[float, str]:
        """
        Make a prediction for a pick sequence.
        
        Args:
            pick_sequence: List of 10 hero IDs in chronological pick order
            
        Returns:
            Tuple of (probability, predicted_winner)
        """
        if len(pick_sequence) != 10:
            raise ValueError(f"Pick sequence must have exactly 10 heroes, got {len(pick_sequence)}")
        
        # Convert to embeddings
        embedded_sequence = []
        for hero_id in pick_sequence:
            hero_str = str(hero_id)
            if hero_str in self.embeddings.key_to_index:
                embedded_sequence.append(self.embeddings[hero_str])
            else:
                # Use zero vector for unknown heroes
                embedded_sequence.append(np.zeros(self.embeddings.vector_size))
        
        embedded_sequence = np.array(embedded_sequence)
        
        # Convert to tensors with positional information
        hero_tensor = torch.FloatTensor(embedded_sequence).unsqueeze(0).to(self.device)  # (1, 10, 150)
        position_tensor = torch.LongTensor(list(range(10))).unsqueeze(0).to(self.device)  # (1, 10)

        # Make prediction
        with torch.no_grad():
            prediction = self.model(hero_tensor, position_tensor)
            probability = prediction.cpu().item()
        
        predicted_winner = "Radiant" if probability > 0.5 else "Dire"
        
        return probability, predicted_winner

@pytest.fixture
def lstm_predictor():
    """Create LSTM predictor instance."""
    model_path = "models/lstm_model.pth"
    embeddings_path = "models/hero_embeddings.kv"
    
    if not os.path.exists(model_path):
        pytest.skip(f"LSTM model not found at {model_path}")
    
    if not os.path.exists(embeddings_path):
        pytest.skip(f"Hero embeddings not found at {embeddings_path}")
    
    try:
        return LSTMPredictor(model_path, embeddings_path)
    except Exception as e:
        pytest.fail(f"Failed to create LSTM predictor: {e}")

def test_predictor_initialization(lstm_predictor):
    """Test that the LSTM predictor initializes correctly."""
    assert lstm_predictor.model is not None
    assert lstm_predictor.embeddings is not None
    assert isinstance(lstm_predictor.model, CBOWLSTMModel)
    assert isinstance(lstm_predictor.embeddings, KeyedVectors)

def test_valid_prediction(lstm_predictor):
    """Test prediction with a valid pick sequence."""
    # Sample pick sequence from our test data
    pick_sequence = [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
    
    probability, predicted_winner = lstm_predictor.predict(pick_sequence)
    
    # Validate output
    assert isinstance(probability, float), "Probability should be a float"
    assert 0 <= probability <= 1, f"Probability {probability} should be between 0 and 1"
    assert predicted_winner in ["Radiant", "Dire"], f"Predicted winner should be 'Radiant' or 'Dire', got '{predicted_winner}'"
    
    logger.info(f"Prediction for {pick_sequence}: {probability:.4f} ({predicted_winner})")

def test_multiple_predictions(lstm_predictor):
    """Test predictions with multiple different pick sequences."""
    test_sequences = [
        [31, 138, 121, 2, 56, 114, 137, 52, 8, 90],    # Test sequence 1
        [74, 5, 22, 99, 27, 35, 49, 44, 120, 10],      # Test sequence 2
        [101, 19, 7, 9, 104, 95, 135, 48, 39, 22],     # Test sequence 3
    ]
    
    predictions = []
    
    for i, sequence in enumerate(test_sequences):
        probability, predicted_winner = lstm_predictor.predict(sequence)
        predictions.append((probability, predicted_winner))
        
        # Validate each prediction
        assert isinstance(probability, float), f"Prediction {i+1}: Probability should be a float"
        assert 0 <= probability <= 1, f"Prediction {i+1}: Probability {probability} should be between 0 and 1"
        assert predicted_winner in ["Radiant", "Dire"], f"Prediction {i+1}: Invalid winner '{predicted_winner}'"
        
        logger.info(f"Sequence {i+1}: {probability:.4f} ({predicted_winner})")
    
    # Check that we got different predictions (not all identical)
    probabilities = [p[0] for p in predictions]
    assert len(set(probabilities)) > 1 or len(probabilities) == 1, "Should get varied predictions for different sequences"

def test_invalid_sequence_length(lstm_predictor):
    """Test that invalid sequence lengths raise appropriate errors."""
    # Test with too few heroes
    with pytest.raises(ValueError, match="Pick sequence must have exactly 10 heroes"):
        lstm_predictor.predict([1, 2, 3, 4, 5])
    
    # Test with too many heroes
    with pytest.raises(ValueError, match="Pick sequence must have exactly 10 heroes"):
        lstm_predictor.predict([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])
    
    # Test with empty sequence
    with pytest.raises(ValueError, match="Pick sequence must have exactly 10 heroes"):
        lstm_predictor.predict([])

def test_unknown_heroes_handling(lstm_predictor):
    """Test that unknown hero IDs are handled gracefully."""
    # Use some very high hero IDs that likely don't exist in embeddings
    pick_sequence = [9999, 9998, 9997, 9996, 9995, 9994, 9993, 9992, 9991, 9990]
    
    # Should not raise an error
    probability, predicted_winner = lstm_predictor.predict(pick_sequence)
    
    # Should still return valid output
    assert isinstance(probability, float), "Should return valid probability even with unknown heroes"
    assert 0 <= probability <= 1, "Probability should be valid even with unknown heroes"
    assert predicted_winner in ["Radiant", "Dire"], "Should return valid winner even with unknown heroes"
    
    logger.info(f"Prediction with unknown heroes: {probability:.4f} ({predicted_winner})")

def test_mixed_known_unknown_heroes(lstm_predictor):
    """Test prediction with a mix of known and unknown heroes."""
    # Mix of known heroes (from our training data) and unknown ones
    pick_sequence = [31, 9999, 121, 9998, 56, 114, 9997, 52, 8, 9996]
    
    probability, predicted_winner = lstm_predictor.predict(pick_sequence)
    
    assert isinstance(probability, float), "Should handle mixed known/unknown heroes"
    assert 0 <= probability <= 1, "Probability should be valid with mixed heroes"
    assert predicted_winner in ["Radiant", "Dire"], "Should return valid winner with mixed heroes"

def test_prediction_consistency(lstm_predictor):
    """Test that the same input produces consistent output."""
    pick_sequence = [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
    
    # Make multiple predictions with the same input
    predictions = []
    for _ in range(5):
        probability, predicted_winner = lstm_predictor.predict(pick_sequence)
        predictions.append((probability, predicted_winner))
    
    # All predictions should be identical (model is in eval mode)
    first_prediction = predictions[0]
    for prediction in predictions[1:]:
        assert abs(prediction[0] - first_prediction[0]) < 1e-6, "Predictions should be consistent"
        assert prediction[1] == first_prediction[1], "Predicted winners should be consistent"

def test_endpoint_integration():
    """Test the complete endpoint integration flow."""
    # This test simulates how the LSTM model would be used in the actual bot
    
    model_path = "models/lstm_model.pth"
    embeddings_path = "models/hero_embeddings.kv"
    
    if not os.path.exists(model_path) or not os.path.exists(embeddings_path):
        pytest.skip("Required model files not found")
    
    # Initialize predictor (simulating bot startup)
    predictor = LSTMPredictor(model_path, embeddings_path)
    
    # Sample pick sequence (simulating user input)
    sample_sequence = [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
    
    # Make prediction (simulating bot response)
    probability, predicted_winner = predictor.predict(sample_sequence)
    
    # Format response (simulating bot message formatting)
    confidence = abs(probability - 0.5) * 2
    response_message = f"Predicted winner: {predicted_winner} (confidence: {confidence:.1%})"
    
    # Validate response
    assert isinstance(response_message, str), "Response should be a string"
    assert predicted_winner in response_message, "Response should contain predicted winner"
    assert "confidence" in response_message.lower(), "Response should contain confidence information"
    
    logger.info(f"Bot response: {response_message}")

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
