# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Feature schemas for different prediction models to prevent data leakage.
"""

# Pre-game/draft-phase features only - no post-match statistics
DRAFT_PHASE_FEATURES = [
    'match_id',
    # Radiant team hero information
    'radiant_player_1_hero_id',
    'radiant_player_2_hero_id', 
    'radiant_player_3_hero_id',
    'radiant_player_4_hero_id',
    'radiant_player_5_hero_id',
    
    # Dire team hero information
    'dire_player_1_hero_id',
    'dire_player_2_hero_id',
    'dire_player_3_hero_id', 
    'dire_player_4_hero_id',
    'dire_player_5_hero_id',
    
    # Player-hero expertise (games played, winrate with specific hero)
    'radiant_player_1_hero_games_played',
    'radiant_player_1_hero_winrate',
    'radiant_player_1_last_played',
    'radiant_player_2_hero_games_played',
    'radiant_player_2_hero_winrate', 
    'radiant_player_2_last_played',
    'radiant_player_3_hero_games_played',
    'radiant_player_3_hero_winrate',
    'radiant_player_3_last_played',
    'radiant_player_4_hero_games_played',
    'radiant_player_4_hero_winrate',
    'radiant_player_4_last_played',
    'radiant_player_5_hero_games_played',
    'radiant_player_5_hero_winrate',
    'radiant_player_5_last_played',
    
    'dire_player_1_hero_games_played',
    'dire_player_1_hero_winrate',
    'dire_player_1_last_played',
    'dire_player_2_hero_games_played',
    'dire_player_2_hero_winrate',
    'dire_player_2_last_played',
    'dire_player_3_hero_games_played',
    'dire_player_3_hero_winrate',
    'dire_player_3_last_played',
    'dire_player_4_hero_games_played',
    'dire_player_4_hero_winrate',
    'dire_player_4_last_played',
    'dire_player_5_hero_games_played',
    'dire_player_5_hero_winrate',
    'dire_player_5_last_played',
    
    # Global hero winrates in current meta
    'radiant_player_1_hero_global_winrate',
    'radiant_player_2_hero_global_winrate',
    'radiant_player_3_hero_global_winrate',
    'radiant_player_4_hero_global_winrate',
    'radiant_player_5_hero_global_winrate',
    
    'dire_player_1_hero_global_winrate',
    'dire_player_2_hero_global_winrate',
    'dire_player_3_hero_global_winrate',
    'dire_player_4_hero_global_winrate',
    'dire_player_5_hero_global_winrate',
    
    # Target variable
    'radiant_win'
]

# Features that should NEVER be used in draft-phase prediction (post-match stats)
FORBIDDEN_FEATURES = [
    # In-game statistics - only available during/after match
    'kills', 'deaths', 'assists', 'gold_per_min', 'xp_per_min', 
    'net_worth', 'last_hits', 'denies', 'level', 'hero_damage',
    'tower_damage', 'teamfight_participation', 'obs_placed', 'sen_placed',
    'roshans_killed', 'hero_healing',
    
    # Team aggregated statistics (computed from post-match data)
    'team_kills_avg', 'team_deaths_avg', 'team_assists_avg',
    'team_gold_per_min_avg', 'team_xp_per_min_avg', 'team_net_worth_avg',
    'team_last_hits_avg', 'team_denies_avg', 'team_level_avg',
    'team_hero_damage_avg', 'team_tower_damage_avg', 'team_kda_avg',
    'team_obs_placed_sum', 'team_sen_placed_sum', 'team_roshans_killed_sum',
    'team_teamfight_participation_sum'
]

# Live/in-game features for Dota Plus style predictions (during match)
LIVE_GAME_FEATURES = [
    'match_id',
    # Hero information (available from draft)
    'radiant_player_1_hero_id', 'radiant_player_2_hero_id', 'radiant_player_3_hero_id',
    'radiant_player_4_hero_id', 'radiant_player_5_hero_id',
    'dire_player_1_hero_id', 'dire_player_2_hero_id', 'dire_player_3_hero_id',
    'dire_player_4_hero_id', 'dire_player_5_hero_id',
    
    # Current match statistics (available during game)
    'radiant_player_1_kills', 'radiant_player_1_deaths', 'radiant_player_1_assists',
    'radiant_player_1_gold_per_min', 'radiant_player_1_xp_per_min',
    'radiant_player_1_net_worth', 'radiant_player_1_last_hits',
    'radiant_player_1_denies', 'radiant_player_1_level',
    # ... (repeat for all players)
    
    # Team aggregated live statistics
    'radiant_team_kills_avg', 'radiant_team_deaths_avg', 'radiant_team_assists_avg',
    'radiant_team_gold_per_min_avg', 'radiant_team_xp_per_min_avg',
    'radiant_team_net_worth_avg', 'radiant_team_last_hits_avg',
    'radiant_team_denies_avg', 'radiant_team_level_avg',
    
    'dire_team_kills_avg', 'dire_team_deaths_avg', 'dire_team_assists_avg',
    'dire_team_gold_per_min_avg', 'dire_team_xp_per_min_avg',
    'dire_team_net_worth_avg', 'dire_team_last_hits_avg',
    'dire_team_denies_avg', 'dire_team_level_avg',
    
    # Target
    'radiant_win'
]

def validate_features_for_draft_prediction(feature_list):
    """
    Validate that no post-match features are used in draft-phase prediction.
    
    Args:
        feature_list: List of feature names to validate
        
    Returns:
        tuple: (is_valid, forbidden_features_found)
    """
    forbidden_found = []
    
    for feature in feature_list:
        for forbidden_pattern in FORBIDDEN_FEATURES:
            if forbidden_pattern in feature:
                forbidden_found.append(feature)
                break
    
    return len(forbidden_found) == 0, forbidden_found

def get_draft_phase_features():
    """Return the whitelist of allowed draft-phase features."""
    return [f for f in DRAFT_PHASE_FEATURES if f != 'radiant_win']

def get_draft_phase_target():
    """Return the target variable for draft-phase prediction."""
    return 'radiant_win'

def get_v2_enhanced_features():
    """Return additional features for draft model v2."""
    return [
        # Player expertise features (already in DRAFT_PHASE_FEATURES)
        'radiant_player_1_hero_games_played', 'radiant_player_1_hero_winrate', 'radiant_player_1_last_played',
        'radiant_player_2_hero_games_played', 'radiant_player_2_hero_winrate', 'radiant_player_2_last_played',
        'radiant_player_3_hero_games_played', 'radiant_player_3_hero_winrate', 'radiant_player_3_last_played',
        'radiant_player_4_hero_games_played', 'radiant_player_4_hero_winrate', 'radiant_player_4_last_played',
        'radiant_player_5_hero_games_played', 'radiant_player_5_hero_winrate', 'radiant_player_5_last_played',

        'dire_player_1_hero_games_played', 'dire_player_1_hero_winrate', 'dire_player_1_last_played',
        'dire_player_2_hero_games_played', 'dire_player_2_hero_winrate', 'dire_player_2_last_played',
        'dire_player_3_hero_games_played', 'dire_player_3_hero_winrate', 'dire_player_3_last_played',
        'dire_player_4_hero_games_played', 'dire_player_4_hero_winrate', 'dire_player_4_last_played',
        'dire_player_5_hero_games_played', 'dire_player_5_hero_winrate', 'dire_player_5_last_played',

        # Global hero win rates (already in DRAFT_PHASE_FEATURES)
        'radiant_player_1_hero_global_winrate', 'radiant_player_2_hero_global_winrate',
        'radiant_player_3_hero_global_winrate', 'radiant_player_4_hero_global_winrate',
        'radiant_player_5_hero_global_winrate',
        'dire_player_1_hero_global_winrate', 'dire_player_2_hero_global_winrate',
        'dire_player_3_hero_global_winrate', 'dire_player_4_hero_global_winrate',
        'dire_player_5_hero_global_winrate',

        # Team-level aggregations
        'radiant_team_avg_hero_games_played', 'radiant_team_avg_hero_winrate',
        'radiant_team_avg_global_winrate', 'radiant_team_std_hero_winrate',
        'radiant_team_min_hero_games_played', 'radiant_team_max_hero_games_played',

        'dire_team_avg_hero_games_played', 'dire_team_avg_hero_winrate',
        'dire_team_avg_global_winrate', 'dire_team_std_hero_winrate',
        'dire_team_min_hero_games_played', 'dire_team_max_hero_games_played',

        # Meta features
        'match_patch_version', 'tournament_tier', 'tournament_month'
    ]

def get_v3_advanced_features():
    """Return advanced features for draft model v3 (R&D track)."""
    return [
        # Hero synergy features
        'radiant_team_synergy_score', 'dire_team_synergy_score',
        'radiant_best_combo_winrate', 'dire_best_combo_winrate',
        'radiant_worst_combo_winrate', 'dire_worst_combo_winrate',

        # Counter-pick features
        'radiant_vs_dire_matchup_advantage', 'dire_vs_radiant_matchup_advantage',
        'radiant_team_counter_score', 'dire_team_counter_score',

        # Role-based features
        'radiant_carry_strength', 'radiant_support_strength',
        'radiant_mid_strength', 'radiant_offlane_strength',
        'dire_carry_strength', 'dire_support_strength',
        'dire_mid_strength', 'dire_offlane_strength',

        # Temporal features
        'days_since_patch', 'tournament_day', 'meta_shift_indicator'
    ]
