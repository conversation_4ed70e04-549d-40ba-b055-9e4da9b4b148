# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Train CBOW-LSTM model for draft-phase prediction using PyTorch.
Integrates positional embeddings and hero counter-rate feature.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import logging
import os
import sys
from typing import Tuple, List
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, log_loss
from gensim.models import KeyedVectors
import pickle
import argparse

from tqdm import tqdm
from torch.cuda.amp import autocast, GradScaler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Device selection
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# logger.info(f"Using device: {device}")

# Fixed pick order positions for Radiant and Dire
RADIANT_POS = {0, 3, 4, 7, 8}
DIRE_POS = {1, 2, 5, 6, 9}

class DraftSequenceDataset(Dataset):
    """Dataset for draft sequences with hero embeddings, positional embeddings, and counter-rates."""
    def __init__(self, sequences: List[List[int]], labels: List[int], embeddings: KeyedVectors):
        self.sequences = sequences
        self.labels = labels
        self.embeddings = embeddings
        self.embedding_dim = embeddings.vector_size

        # Load hero counter-rate matrix
        counter_path = "dataset/train_data/hero_counter_rates.csv"
        self.counter_matrix = pd.read_csv(counter_path, index_col=0)

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]

        # Hero embeddings
        embedded_sequence = []
        for hero_id in sequence:
            h = str(hero_id)
            if h in self.embeddings.key_to_index:
                embedded_sequence.append(self.embeddings[h])
            else:
                embedded_sequence.append(np.zeros(self.embedding_dim))
        embedded_sequence = np.array(embedded_sequence)  # (10, 150)

        # Positional embeddings (0-9)
        positional_sequence = np.arange(10)  # use in model

        # Counter-rate feature per pick
        radiant_ids = [sequence[i] for i in RADIANT_POS]
        dire_ids = [sequence[i] for i in DIRE_POS]
        rates = []
        for i, hero_id in enumerate(sequence):
            others = dire_ids if i in RADIANT_POS else radiant_ids
            # handle missing heroes
            try:
                row = self.counter_matrix.loc[int(hero_id), [int(o) for o in others]]
                rates.append(row.mean())
            except Exception:
                rates.append(0.0)
        rates = np.array(rates)  # (10,)

        return (
            torch.FloatTensor(embedded_sequence),        # (10,150)
            torch.LongTensor(positional_sequence),       # (10,)
            torch.FloatTensor(rates).unsqueeze(1),       # (10,1)
            torch.FloatTensor([label])                   # (1,)
        )

class CBOWLSTMModel(nn.Module):
    """CBOW-LSTM model with positional embeddings and counter-rate feature."""
    def __init__(self, embedding_dim=150, lstm_hidden_size=128,
                 pos_embedding_dim=8, dropout_rate=0.15):
        super(CBOWLSTMModel, self).__init__()
        self.pos_embedding = nn.Embedding(10, pos_embedding_dim)
        input_size = embedding_dim + pos_embedding_dim + 1

        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=lstm_hidden_size,
            num_layers=2,
            dropout=dropout_rate,
            batch_first=True
        )

        self.fc1 = nn.Linear(lstm_hidden_size, 64)
        self.fc2 = nn.Linear(64, 32)
        self.out = nn.Linear(32, 1)

        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_rate)
        # removed sigmoid for BCEWithLogitsLoss compatibility

    def forward(self, hero_seq, pos_seq, rate_seq):
        # hero_seq: (batch,10,150), pos_seq: (batch,10), rate_seq: (batch,10,1)
        pos_emb = self.pos_embedding(pos_seq)  # (batch,10, pos_emb_dim)
        x = torch.cat([hero_seq, pos_emb, rate_seq], dim=2)  # (batch,10, input_size)
        
        lstm_out, (hn, _) = self.lstm(x)
        h_final = hn[-1]  # (batch, hidden_size)

        x = self.relu(self.fc1(h_final))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.out(x)
        return x

def load_data() -> Tuple[List[List[int]], List[int]]:
    df = pd.read_csv("dataset/train_data/sequential_drafts_with_features.csv")

    # Combine radiant and dire picks into alternating sequence (R1, D1, D2, R2, R3, D3, D4, R4, R5, D5)
    sequences = []
    for _, row in df.iterrows():
        radiant_picks = [int(x) for x in row['radiant_pick_sequence'].split(',')]
        dire_picks = [int(x) for x in row['dire_pick_sequence'].split(',')]

        # Create alternating sequence following draft order
        sequence = [
            radiant_picks[0],  # R1
            dire_picks[0],     # D1
            dire_picks[1],     # D2
            radiant_picks[1],  # R2
            radiant_picks[2],  # R3
            dire_picks[2],     # D3
            dire_picks[3],     # D4
            radiant_picks[3],  # R4
            radiant_picks[4],  # R5
            dire_picks[4]      # D5
        ]
        sequences.append(sequence)

    labels = df['radiant_win'].astype(int).tolist()
    return sequences, labels

def load_embeddings() -> KeyedVectors:
    emb = KeyedVectors.load("models/hero_embeddings.kv")
    return emb

def create_data_loaders(sequences, labels, embeddings, test_size=0.2, batch_size=1024):
    stratify = labels if min(labels.count(0), labels.count(1)) > 1 else None
    X_train, X_test, y_train, y_test = train_test_split(
        sequences, labels, test_size=test_size, random_state=42, stratify=stratify
    )
    train_ds = DraftSequenceDataset(X_train, y_train, embeddings)
    test_ds = DraftSequenceDataset(X_test, y_test, embeddings)
    # Optimize data loading for GPU
    train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=True, num_workers=4, pin_memory=True)
    test_loader = DataLoader(test_ds, batch_size=batch_size, shuffle=False, num_workers=4, pin_memory=True)
    return train_loader, test_loader

def train_model(model, train_loader, test_loader, num_epochs=150, lr=1e-4):
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5, min_lr=1e-6)
    scaler = GradScaler()

    model.to(device)
    best_loss = float('inf')
    patience_ctr = 0

    for epoch in range(num_epochs):
        model.train()
        for hero_seq, pos_seq, rate_seq, labels in tqdm(train_loader, desc=f"Train Epoch {epoch+1}/{num_epochs}", leave=False):
            hero_seq = hero_seq.to(device)
            pos_seq = pos_seq.to(device)
            rate_seq = rate_seq.to(device)
            labels = labels.to(device)

            optimizer.zero_grad()
            with autocast():
                outputs = model(hero_seq, pos_seq, rate_seq)
                loss = criterion(outputs, labels)
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for hero_seq, pos_seq, rate_seq, labels in tqdm(test_loader, desc=f"Val   Epoch {epoch+1}/{num_epochs}", leave=False):
                hero_seq = hero_seq.to(device)
                pos_seq = pos_seq.to(device)
                rate_seq = rate_seq.to(device)
                labels = labels.to(device)
                with autocast():
                    outputs = model(hero_seq, pos_seq, rate_seq)
                    val_loss += criterion(outputs, labels).item()
        avg_val = val_loss / len(test_loader)
        scheduler.step(avg_val)

        if avg_val < best_loss:
            best_loss = avg_val
            torch.save(model.state_dict(), "models/lstm_model_best.pth")
            patience_ctr = 0
        else:
            patience_ctr += 1
            if patience_ctr >= 30:
                break

    model.load_state_dict(torch.load("models/lstm_model_best.pth"))
    return model


def evaluate_model(model, test_loader):
    model.eval()
    preds, probs, labs = [], [], []
    with torch.no_grad():
        for hero_seq, pos_seq, rate_seq, labels in test_loader:
            hero_seq = hero_seq.to(device)
            pos_seq = pos_seq.to(device)
            rate_seq = rate_seq.to(device)
            labels = labels.to(device)
            outputs = model(hero_seq, pos_seq, rate_seq)
            probs.extend(outputs.cpu().numpy().flatten().tolist())
            preds.extend((outputs > 0.5).float().cpu().numpy().flatten().tolist())
            labs.extend(labels.cpu().numpy().flatten().tolist())

    acc = accuracy_score(labs, preds)
    try:
        auc = roc_auc_score(labs, probs)
    except:
        auc = 0.5
    try:
        ll = log_loss(labs, probs)
    except:
        ll = float('inf')
    return {'accuracy': acc, 'roc_auc': auc, 'log_loss': ll}

def save_model(model, path="models/lstm_model.pth"):
    torch.save({
        'model_state_dict': model.state_dict(),
        'config': {
            'embedding_dim': 150,
            'lstm_hidden_size': 128,
            'pos_embedding_dim': 8,
            'dropout_rate': 0.15
        }
    }, path)

def train_lstm_model(num_epochs=150):
    sequences, labels = load_data()
    embeddings = load_embeddings()
    train_loader, test_loader = create_data_loaders(sequences, labels, embeddings)
    model = CBOWLSTMModel()
    model = train_model(model, train_loader, test_loader, num_epochs=num_epochs)
    metrics = evaluate_model(model, test_loader)
    save_model(model)
    with open("models/lstm_metrics.pkl", "wb") as f:
        pickle.dump(metrics, f)
    logger.info(f"Trained LSTM with accuracy={metrics['accuracy']:.4f}, AUC={metrics['roc_auc']:.4f}")
    return model, metrics

def parse_args():
    parser = argparse.ArgumentParser(description="Train CBOW-LSTM model for Dota 2 draft phase")
    parser.add_argument('--epochs', type=int, default=150, help='Number of training epochs')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    logger.info(f"Using device: {device}")
    train_lstm_model(num_epochs=args.epochs)
