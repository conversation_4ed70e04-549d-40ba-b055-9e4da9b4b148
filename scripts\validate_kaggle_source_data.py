import pandas as pd
import glob
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define paths
KAGGLE_DATA_DIR = Path("dataset/kaggle_data")
OUTPUT_REPORT_PATH = Path("reports/kaggle_source_data_validation_report.md")

def get_all_kaggle_folders():
    """Find all monthly data folders in the Kaggle directory."""
    return [p for p in KAGGLE_DATA_DIR.iterdir() if p.is_dir()]

def validate_source_files(folder: Path):
    """
    Validates the source Kaggle CSV files for a given month.
    Analyzes draft timings and metadata to verify critical assumptions.
    """
    draft_timings_path = folder / "draft_timings.csv"
    metadata_path = folder / "main_metadata.csv"

    if not draft_timings_path.exists() or not metadata_path.exists():
        logging.warning(f"Skipping {folder.name}: Missing required CSV files.")
        return None

    logging.info(f"Processing folder: {folder.name}")
    
    try:
        draft_df = pd.read_csv(draft_timings_path)
        meta_df = pd.read_csv(metadata_path)
    except Exception as e:
        logging.error(f"Could not read CSVs in {folder.name}: {e}")
        return None

    # 1. Schema Consistency Check
    required_draft_cols = {'match_id', 'order', 'pick', 'hero_id'}
    required_meta_cols = {'match_id', 'radiant_win'}
    
    if not required_draft_cols.issubset(draft_df.columns):
        logging.error(f"Draft CSV in {folder.name} is missing columns. Found: {draft_df.columns}")
        return None
    if not required_meta_cols.issubset(meta_df.columns):
        logging.error(f"Metadata CSV in {folder.name} is missing columns. Found: {meta_df.columns}")
        return None

    # Filter for picks only
    picks_df = draft_df[draft_df['pick']].copy()

    # 2. Pick Count Distribution
    pick_counts = picks_df.groupby('match_id').size()
    
    # 3. Hero ID Validity (Robust Check)
    original_hero_ids = picks_df['hero_id']
    numeric_hero_ids = pd.to_numeric(original_hero_ids, errors='coerce')
    invalid_hero_ids = picks_df[numeric_hero_ids.isna() | (numeric_hero_ids <= 0)]

    # Merge with metadata to get win info
    merged_df = picks_df.merge(meta_df[['match_id', 'radiant_win']], on='match_id', how='left')
    
    # 4. Team Pick Order Analysis
    team_pick_order_present = 'team' in picks_df.columns
    
    report = {
        "folder": folder.name,
        "total_matches_in_drafts": picks_df['match_id'].nunique(),
        "total_matches_in_metadata": meta_df['match_id'].nunique(),
        "matches_with_10_picks": (pick_counts == 10).sum(),
        "matches_with_other_pick_counts": (pick_counts != 10).sum(),
        "pick_count_distribution": pick_counts.value_counts().to_dict(),
        "invalid_hero_id_count": len(invalid_hero_ids),
        "team_pick_order_present": team_pick_order_present,
        "radiant_win_info_missing": merged_df['radiant_win'].isnull().sum()
    }
    
    return report

def generate_report(all_reports):
    """Generates a markdown report from the validation results."""
    if not all_reports:
        logging.warning("No reports were generated. Cannot create markdown report.")
        return

    report_md = "# Kaggle Source Data Validation Report\n\n"
    report_md += "This report analyzes the raw Kaggle CSV files to verify key assumptions about the data structure before it is processed for model training.\n\n"

    total_matches = 0
    total_10_picks = 0
    total_other_picks = 0
    
    for report in all_reports:
        total_matches += report['total_matches_in_drafts']
        total_10_picks += report['matches_with_10_picks']
        total_other_picks += report['matches_with_other_pick_counts']

        report_md += f"## Folder: `{report['folder']}`\n"
        report_md += f"- **Total Matches Analyzed**: {report['total_matches_in_drafts']}\n"
        report_md += f"- **Matches with Exactly 10 Picks**: {report['matches_with_10_picks']} ({report['matches_with_10_picks'] / report['total_matches_in_drafts']:.2%})\n"
        report_md += f"- **Matches with Other Pick Counts**: {report['matches_with_other_pick_counts']}\n"
        report_md += f"- **Team Pick Order Column ('team') Present**: {'✅ Yes' if report['team_pick_order_present'] else '❌ No'}\n"
        report_md += f"- **Invalid Hero IDs Found**: {report['invalid_hero_id_count']}\n"
        report_md += f"- **Matches Missing Win/Loss Info**: {report['radiant_win_info_missing']}\n"
        report_md += "- **Pick Count Distribution**:\n"
        for count, num_matches in sorted(report['pick_count_distribution'].items()):
            report_md += f"  - `{count}` picks: {num_matches} matches\n"
        report_md += "\n"

    report_md += "## Overall Summary\n"
    report_md += f"- **Total Matches Across All Folders**: {total_matches}\n"
    report_md += f"- **Total Matches with 10 Picks**: {total_10_picks} ({total_10_picks/total_matches:.2%})\n"
    report_md += f"- **Total Matches with Other Pick Counts**: {total_other_picks}\n"
    
    avg_success_rate = total_10_picks / total_matches if total_matches > 0 else 0
    report_md += f"- **Overall Success Rate (10 picks)**: {avg_success_rate:.2%}\n\n"
    
    # Key Findings
    report_md += "### Key Findings & Recommendations\n"
    team_col_present_in_all = all(r['team_pick_order_present'] for r in all_reports)
    if not team_col_present_in_all:
        report_md += "- **Critical Finding**: The `team` column, which specifies whether Radiant or Dire made a pick, is **MISSING** from the source data. The model currently cannot distinguish which team made each pick. This confirms a major assumption is invalid.\n"
        report_md += "  - **Recommendation**: The chronological sequence is still valuable, but the model's understanding of draft symmetry (e.g., first-pick advantage) is limited. This must be acknowledged in the model's evaluation.\n"
    else:
        report_md += "- **Positive Finding**: The `team` column is present, allowing for richer feature engineering around draft symmetry.\n"

    if avg_success_rate < 0.95:
        report_md += f"- **Data Quality Issue**: The data processing success rate is {avg_success_rate:.2%}, slightly lower than the documented ~92%. The primary reason appears to be matches not having exactly 10 picks. This confirms the source of data loss.\n"
        report_md += "  - **Recommendation**: The current data cleaning step of filtering for 10-pick matches is correct. No changes are needed, but this confirms the dataset size limitations.\n"

    with open(OUTPUT_REPORT_PATH, "w", encoding="utf-8") as f:
        f.write(report_md)
    
    logging.info(f"Validation report saved to: {OUTPUT_REPORT_PATH}")

def main():
    """Main function to run the validation process."""
    logging.info("Starting Kaggle source data validation...")
    folders = get_all_kaggle_folders()
    
    if not folders:
        logging.error("No Kaggle data folders found in `dataset/kaggle_data`. Aborting.")
        return

    all_reports = []
    for folder in folders:
        report = validate_source_files(folder)
        if report:
            all_reports.append(report)
            
    generate_report(all_reports)
    logging.info("Validation process completed.")

if __name__ == "__main__":
    main()
