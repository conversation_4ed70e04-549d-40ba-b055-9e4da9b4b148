# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
LSTM Model Integration Example
Shows how to integrate the LSTM model with the existing prediction system.
"""

import torch
import numpy as np
import logging
import os
import sys
from typing import List, Tuple, Optional

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml.create_model_lstm import CBOWLSTMModel
from gensim.models import KeyedVectors

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LSTMDraftPredictor:
    """
    LSTM-based draft prediction service.
    Integrates with the existing prediction system architecture.
    """
    
    def __init__(self, model_path: str = "models/lstm_model.pth", 
                 embeddings_path: str = "models/hero_embeddings.kv"):
        """
        Initialize the LSTM predictor.
        
        Args:
            model_path: Path to the trained LSTM model
            embeddings_path: Path to the hero embeddings
        """
        self.model_path = model_path
        self.embeddings_path = embeddings_path
        self.model = None
        self.embeddings = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load model and embeddings
        self._load_model()
        self._load_embeddings()
        
        logger.info(f"LSTM Draft Predictor initialized on {self.device}")
    
    def _load_model(self):
        """Load the trained LSTM model."""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            self.model = CBOWLSTMModel(
                embedding_dim=150,
                lstm_hidden_size=150,
                dropout_rate=0.3
            )
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            logger.info("✅ LSTM model loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load LSTM model: {e}")
            raise
    
    def _load_embeddings(self):
        """Load hero embeddings."""
        try:
            self.embeddings = KeyedVectors.load(self.embeddings_path)
            logger.info(f"✅ Hero embeddings loaded: {len(self.embeddings.key_to_index)} heroes")
        except Exception as e:
            logger.error(f"❌ Failed to load hero embeddings: {e}")
            raise
    
    def predict_draft_outcome(self, pick_sequence: List[int]) -> dict:
        """
        Predict draft outcome based on pick sequence.
        
        Args:
            pick_sequence: List of 10 hero IDs in chronological pick order
            
        Returns:
            Dictionary with prediction results
        """
        if len(pick_sequence) != 10:
            raise ValueError(f"Pick sequence must have exactly 10 heroes, got {len(pick_sequence)}")
        
        try:
            # Convert to embeddings
            embedded_sequence = self._sequence_to_embeddings(pick_sequence)
            
            # Split into team picks
            radiant_picks = embedded_sequence[:5]  # First 5 picks
            dire_picks = embedded_sequence[5:]     # Last 5 picks
            
            # Convert to tensors
            radiant_tensor = torch.FloatTensor(radiant_picks).unsqueeze(0).to(self.device)
            dire_tensor = torch.FloatTensor(dire_picks).unsqueeze(0).to(self.device)
            
            # Make prediction
            with torch.no_grad():
                prediction = self.model(radiant_tensor, dire_tensor)
                probability = prediction.cpu().item()
            
            # Format results
            predicted_winner = "Radiant" if probability > 0.5 else "Dire"
            confidence = abs(probability - 0.5) * 2
            
            return {
                'predicted_winner': predicted_winner,
                'radiant_win_probability': probability,
                'dire_win_probability': 1 - probability,
                'confidence': confidence,
                'model_type': 'LSTM',
                'pick_sequence': pick_sequence
            }
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    def _sequence_to_embeddings(self, pick_sequence: List[int]) -> np.ndarray:
        """Convert pick sequence to embeddings."""
        embedded_sequence = []
        
        for hero_id in pick_sequence:
            hero_str = str(hero_id)
            if hero_str in self.embeddings.key_to_index:
                embedded_sequence.append(self.embeddings[hero_str])
            else:
                # Use zero vector for unknown heroes
                embedded_sequence.append(np.zeros(self.embeddings.vector_size))
                logger.warning(f"Unknown hero {hero_id}, using zero vector")
        
        return np.array(embedded_sequence)
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        param_count = sum(p.numel() for p in self.model.parameters()) if self.model else 0
        
        return {
            'model_type': 'CBOW-LSTM',
            'parameters': param_count,
            'embedding_dimension': 150,
            'lstm_hidden_size': 150,
            'vocabulary_size': len(self.embeddings.key_to_index) if self.embeddings else 0,
            'device': str(self.device)
        }

# Integration with existing prediction system
class EnhancedPredictionSystem:
    """
    Enhanced prediction system that combines LSTM with existing models.
    Example of how to integrate LSTM into the current architecture.
    """
    
    def __init__(self):
        """Initialize the enhanced prediction system."""
        self.lstm_predictor = None
        self.xgb_predictor = None  # Placeholder for existing XGBoost model
        
        # Initialize LSTM predictor if available
        try:
            self.lstm_predictor = LSTMDraftPredictor()
            logger.info("✅ LSTM predictor initialized")
        except Exception as e:
            logger.warning(f"LSTM predictor not available: {e}")
        
        # Initialize XGBoost predictor (placeholder)
        # self.xgb_predictor = load_existing_xgb_model()
    
    def predict_match_outcome(self, match_data: dict) -> dict:
        """
        Predict match outcome using available models.
        
        Args:
            match_data: Dictionary containing match information
            
        Returns:
            Combined prediction results
        """
        results = {
            'predictions': {},
            'ensemble_prediction': None,
            'confidence': 0.0
        }
        
        # LSTM prediction (if pick sequence available)
        if self.lstm_predictor and 'pick_sequence' in match_data:
            try:
                lstm_result = self.lstm_predictor.predict_draft_outcome(match_data['pick_sequence'])
                results['predictions']['lstm'] = lstm_result
                logger.info(f"LSTM prediction: {lstm_result['predicted_winner']} ({lstm_result['confidence']:.1%})")
            except Exception as e:
                logger.error(f"LSTM prediction failed: {e}")
        
        # XGBoost prediction (placeholder)
        if self.xgb_predictor and 'features' in match_data:
            try:
                # xgb_result = self.xgb_predictor.predict(match_data['features'])
                # results['predictions']['xgboost'] = xgb_result
                pass
            except Exception as e:
                logger.error(f"XGBoost prediction failed: {e}")
        
        # Ensemble prediction (simple example)
        if 'lstm' in results['predictions']:
            # For now, just use LSTM prediction
            lstm_pred = results['predictions']['lstm']
            results['ensemble_prediction'] = {
                'predicted_winner': lstm_pred['predicted_winner'],
                'confidence': lstm_pred['confidence'],
                'method': 'LSTM-only'
            }
        
        return results

# Example usage functions
def example_single_prediction():
    """Example of making a single prediction."""
    logger.info("Example: Single LSTM prediction")
    
    try:
        predictor = LSTMDraftPredictor()
        
        # Sample pick sequence
        pick_sequence = [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
        
        result = predictor.predict_draft_outcome(pick_sequence)
        
        logger.info(f"Pick sequence: {pick_sequence}")
        logger.info(f"Predicted winner: {result['predicted_winner']}")
        logger.info(f"Confidence: {result['confidence']:.1%}")
        logger.info(f"Radiant win probability: {result['radiant_win_probability']:.4f}")
        
        return result
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        return None

def example_enhanced_system():
    """Example of using the enhanced prediction system."""
    logger.info("Example: Enhanced prediction system")
    
    try:
        system = EnhancedPredictionSystem()
        
        # Sample match data
        match_data = {
            'match_id': 12345,
            'pick_sequence': [31, 138, 121, 2, 56, 114, 137, 52, 8, 90],
            # 'features': [...],  # XGBoost features would go here
        }
        
        results = system.predict_match_outcome(match_data)
        
        logger.info("Prediction Results:")
        for model_name, prediction in results['predictions'].items():
            logger.info(f"  {model_name.upper()}: {prediction['predicted_winner']} "
                       f"({prediction['confidence']:.1%})")
        
        if results['ensemble_prediction']:
            ensemble = results['ensemble_prediction']
            logger.info(f"ENSEMBLE: {ensemble['predicted_winner']} "
                       f"({ensemble['confidence']:.1%}) [{ensemble['method']}]")
        
        return results
        
    except Exception as e:
        logger.error(f"Enhanced system example failed: {e}")
        return None

if __name__ == "__main__":
    # Run examples
    logger.info("LSTM Integration Examples")
    logger.info("=" * 50)
    
    # Single prediction example
    example_single_prediction()
    
    logger.info("\n" + "-" * 50)
    
    # Enhanced system example
    example_enhanced_system()
    
    logger.info("\n🎉 Integration examples completed!")
