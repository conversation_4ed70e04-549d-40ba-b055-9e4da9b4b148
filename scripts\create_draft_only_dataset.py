# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Create draft-only dataset by filtering the existing full dataset.
This removes all post-match statistics and keeps only draft-time information.
"""

import pandas as pd
import numpy as np
import logging
import os
import sys

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dataset.feature_schemas import validate_features_for_draft_prediction, get_draft_phase_features

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_draft_only_dataset():
    """Create draft-only dataset by filtering existing full dataset."""
    logger.info("Creating draft-only dataset from full dataset")
    
    # Load full dataset
    full_dataset_path = "dataset/train_data/all_data_match_predict.csv"
    if not os.path.exists(full_dataset_path):
        raise FileNotFoundError(f"Full dataset not found at {full_dataset_path}")
    
    df = pd.read_csv(full_dataset_path)
    logger.info(f"Loaded full dataset with {len(df)} matches and {len(df.columns)} features")
    
    # Get allowed draft-phase features
    allowed_features = get_draft_phase_features() + ['radiant_win', 'match_id']
    logger.info(f"Expected draft features: {len(allowed_features)}")
    
    # Find which features actually exist in the dataset
    available_draft_features = []
    missing_features = []
    
    for feature in allowed_features:
        if feature in df.columns:
            available_draft_features.append(feature)
        else:
            missing_features.append(feature)
    
    logger.info(f"Available draft features: {len(available_draft_features)} out of {len(allowed_features)} expected")
    if missing_features:
        logger.warning(f"Missing expected features: {missing_features[:10]}...")  # Show first 10
    
    # Create draft-only dataset with available features
    draft_df = df[available_draft_features].copy()
    
    # Add basic hero ID features if they exist (these should be present)
    hero_id_features = [col for col in df.columns if col.endswith('_hero_id')]
    for feature in hero_id_features:
        if feature not in draft_df.columns:
            draft_df[feature] = df[feature]
    
    logger.info(f"Draft dataset features: {draft_df.columns.tolist()}")
    
    # Validate no forbidden features are present
    is_valid, forbidden = validate_features_for_draft_prediction(draft_df.columns.tolist())
    if not is_valid:
        logger.error(f"Draft dataset contains forbidden features: {forbidden}")
        # Remove forbidden features
        for forbidden_feature in forbidden:
            if forbidden_feature in draft_df.columns:
                draft_df = draft_df.drop(columns=[forbidden_feature])
                logger.info(f"Removed forbidden feature: {forbidden_feature}")
        
        # Re-validate
        is_valid, forbidden = validate_features_for_draft_prediction(draft_df.columns.tolist())
        if not is_valid:
            raise ValueError(f"Still contains forbidden features after cleanup: {forbidden}")
    
    # Ensure we have essential columns
    essential_columns = ['match_id', 'radiant_win']
    for col in essential_columns:
        if col not in draft_df.columns:
            if col in df.columns:
                draft_df[col] = df[col]
            else:
                raise ValueError(f"Essential column {col} not found in dataset")
    
    # Check for hero ID columns
    hero_cols = [col for col in draft_df.columns if 'hero_id' in col]
    logger.info(f"Hero ID columns found: {len(hero_cols)}")
    if len(hero_cols) < 10:
        logger.warning(f"Expected 10 hero ID columns, found {len(hero_cols)}: {hero_cols}")
    
    # Clean data
    logger.info("Cleaning draft dataset...")
    
    # Remove rows with missing essential data
    initial_rows = len(draft_df)
    draft_df = draft_df.dropna(subset=['radiant_win'])
    logger.info(f"Removed {initial_rows - len(draft_df)} rows with missing target")
    
    # Convert target to int
    draft_df['radiant_win'] = draft_df['radiant_win'].astype(int)
    
    # Fill missing hero IDs with 0 (unknown hero)
    for col in hero_cols:
        draft_df[col] = draft_df[col].fillna(0).astype(int)
    
    # Save draft-only dataset
    output_path = "dataset/train_data/all_data_draft_phase.csv"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    draft_df.to_csv(output_path, index=False)
    
    logger.info(f"✅ Created draft-only dataset:")
    logger.info(f"  - Path: {output_path}")
    logger.info(f"  - Matches: {len(draft_df)}")
    logger.info(f"  - Features: {len(draft_df.columns)}")
    logger.info(f"  - Target distribution: {draft_df['radiant_win'].value_counts().to_dict()}")
    
    # Show feature summary
    logger.info("Feature summary:")
    for col in sorted(draft_df.columns):
        if col != 'match_id':
            logger.info(f"  - {col}: {draft_df[col].dtype}")
    
    return draft_df

def validate_draft_dataset():
    """Validate the created draft dataset."""
    draft_path = "dataset/train_data/all_data_draft_phase.csv"
    
    if not os.path.exists(draft_path):
        logger.error(f"Draft dataset not found at {draft_path}")
        return False
    
    df = pd.read_csv(draft_path)
    logger.info(f"Validating draft dataset: {len(df)} matches, {len(df.columns)} features")
    
    # Check essential columns
    essential_cols = ['match_id', 'radiant_win']
    for col in essential_cols:
        if col not in df.columns:
            logger.error(f"Missing essential column: {col}")
            return False
    
    # Check hero ID columns
    hero_cols = [col for col in df.columns if 'hero_id' in col]
    if len(hero_cols) < 10:
        logger.warning(f"Only {len(hero_cols)} hero ID columns found, expected 10")
    
    # Validate no forbidden features
    is_valid, forbidden = validate_features_for_draft_prediction(df.columns.tolist())
    if not is_valid:
        logger.error(f"Validation failed - forbidden features found: {forbidden}")
        return False
    
    # Check data quality
    if df['radiant_win'].isnull().any():
        logger.error("Target variable has null values")
        return False
    
    if len(df) < 1000:
        logger.warning(f"Dataset seems small: {len(df)} matches")
    
    logger.info("✅ Draft dataset validation passed")
    return True

if __name__ == "__main__":
    try:
        # Create draft dataset
        draft_df = create_draft_only_dataset()
        
        # Validate it
        if validate_draft_dataset():
            logger.info("🎉 Draft-only dataset created and validated successfully!")
        else:
            logger.error("❌ Draft dataset validation failed")
            
    except Exception as e:
        logger.error(f"❌ Failed to create draft dataset: {e}")
        raise
