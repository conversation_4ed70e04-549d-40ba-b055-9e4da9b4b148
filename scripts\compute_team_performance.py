"""
Compute team performance statistics from historical Kaggle data.

This script processes all available historical data (2023-2025) to calculate:
- Head-to-head win rates between specific team pairs
- Global win rates for each team

The output is saved as a lookup table for use in the main dataset pipeline.
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Tuple
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define paths
KAGGLE_DATA_DIR = Path("dataset/kaggle_data")
OUTPUT_PATH = Path("models/team_performance.csv")

# All available months for historical analysis
ALL_MONTHS = ["2023", "2024", "202501", "202502", "202503", "202504", "202505", "202506", "202507"]

def load_all_team_data() -> pd.DataFrame:
    """Load team match data from all available months."""
    all_dfs = []
    
    for month in ALL_MONTHS:
        month_dir = KAGGLE_DATA_DIR / month
        meta_file = month_dir / "main_metadata.csv"
        
        if meta_file.exists():
            logger.info(f"Loading team data for month: {month}")
            try:
                # Only load the columns we need to save memory
                df = pd.read_csv(meta_file, usecols=['match_id', 'radiant_team_id', 'dire_team_id', 'radiant_win'])
                df['month'] = month  # Track which month this data came from
                all_dfs.append(df)
                logger.info(f"Loaded {len(df)} match records from {month}")
            except Exception as e:
                logger.error(f"Could not read main_metadata.csv in {month}: {e}")
        else:
            logger.warning(f"Warning: missing main_metadata.csv for month {month}")
    
    if not all_dfs:
        raise ValueError("No team data found in any month!")
    
    combined_df = pd.concat(all_dfs, ignore_index=True)
    logger.info(f"Combined dataset has {len(combined_df)} total match records")
    return combined_df

def calculate_team_performance(team_df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate head-to-head win rates and global win rates for teams.
    
    Args:
        team_df: DataFrame with columns ['match_id', 'radiant_team_id', 'dire_team_id', 'radiant_win']
    
    Returns:
        DataFrame with team performance statistics
    """
    logger.info("Calculating team performance statistics...")
    
    # Handle missing team IDs by replacing with -1
    team_df = team_df.copy()
    team_df['radiant_team_id'] = team_df['radiant_team_id'].fillna(-1).astype(int)
    team_df['dire_team_id'] = team_df['dire_team_id'].fillna(-1).astype(int)
    
    # Convert radiant_win to integer (1 for True, 0 for False)
    team_df['radiant_win'] = team_df['radiant_win'].astype(int)
    
    # Remove matches where both teams are unknown
    valid_matches = team_df[
        (team_df['radiant_team_id'] != -1) | (team_df['dire_team_id'] != -1)
    ]
    logger.info(f"Processing {len(valid_matches)} matches with at least one known team")
    
    # Calculate global win rates for each team
    logger.info("Calculating global win rates...")
    global_stats = []
    
    # Get all unique team IDs
    all_teams = set(valid_matches['radiant_team_id'].unique()) | set(valid_matches['dire_team_id'].unique())
    all_teams.discard(-1)  # Remove the "unknown team" placeholder
    
    for team_id in all_teams:
        # Get all matches where this team played
        radiant_matches = valid_matches[valid_matches['radiant_team_id'] == team_id]
        dire_matches = valid_matches[valid_matches['dire_team_id'] == team_id]

        # Calculate wins and total games
        radiant_wins = radiant_matches['radiant_win'].sum()
        dire_wins = len(dire_matches) - dire_matches['radiant_win'].sum()  # Dire wins when radiant_win = 0

        total_wins = radiant_wins + dire_wins
        total_games = len(radiant_matches) + len(dire_matches)

        if total_games > 0:
            global_win_rate = total_wins / total_games
            global_stats.append({
                'team_id': int(team_id),  # Ensure integer type
                'global_win_rate': global_win_rate,
                'total_games': total_games
            })
    
    global_df = pd.DataFrame(global_stats)
    logger.info(f"Calculated global win rates for {len(global_df)} teams")
    
    # Calculate head-to-head win rates
    logger.info("Calculating head-to-head win rates...")
    h2h_stats = []
    
    # Group by team pairs to calculate head-to-head records
    team_pairs = valid_matches.groupby(['radiant_team_id', 'dire_team_id']).agg({
        'radiant_win': ['sum', 'count']
    }).reset_index()
    
    # Flatten column names
    team_pairs.columns = ['team_a', 'team_b', 'team_a_wins', 'total_games']
    
    for _, row in team_pairs.iterrows():
        team_a = row['team_a']
        team_b = row['team_b']
        team_a_wins = row['team_a_wins']
        total_games = row['total_games']
        
        if team_a != -1 and team_b != -1 and total_games > 0:
            h2h_win_rate = team_a_wins / total_games
            h2h_stats.append({
                'team_a': int(team_a),  # Ensure integer type
                'team_b': int(team_b),  # Ensure integer type
                'h2h_win_rate': h2h_win_rate,
                'h2h_total_games': total_games
            })
    
    h2h_df = pd.DataFrame(h2h_stats)
    logger.info(f"Calculated head-to-head records for {len(h2h_df)} team pairs")
    
    # Combine global and head-to-head data
    # For the final output, we'll create a comprehensive table
    result_data = []
    
    # Add global win rates
    for _, row in global_df.iterrows():
        result_data.append({
            'team_id': int(row['team_id']),
            'opponent_team_id': -1,  # -1 indicates this is a global stat
            'win_rate': row['global_win_rate'],
            'total_games': int(row['total_games']),
            'stat_type': 'global'
        })

    # Add head-to-head win rates
    for _, row in h2h_df.iterrows():
        result_data.append({
            'team_id': int(row['team_a']),
            'opponent_team_id': int(row['team_b']),
            'win_rate': row['h2h_win_rate'],
            'total_games': int(row['h2h_total_games']),
            'stat_type': 'h2h'
        })
    
    result_df = pd.DataFrame(result_data)
    
    logger.info(f"Final dataset has {len(result_df)} team performance records")
    logger.info(f"Average global win rate: {global_df['global_win_rate'].mean():.4f}")
    logger.info(f"Average games per team: {global_df['total_games'].mean():.2f}")
    
    return result_df

def save_team_performance_data(performance_df: pd.DataFrame, output_path: Path):
    """Save the team performance data to CSV."""
    logger.info(f"Saving team performance data to {output_path}")
    
    # Ensure output directory exists
    output_path.parent.mkdir(exist_ok=True)
    
    # Save the results
    performance_df.to_csv(output_path, index=False)
    
    logger.info(f"Successfully saved {len(performance_df)} team performance records")

def main():
    """Main function to compute and save team performance statistics."""
    logger.info("Starting team performance computation...")
    
    try:
        # Load all historical team data
        team_df = load_all_team_data()
        
        # Calculate performance statistics
        performance_df = calculate_team_performance(team_df)
        
        # Save results
        save_team_performance_data(performance_df, OUTPUT_PATH)
        
        logger.info("Team performance computation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during computation: {e}")
        raise

if __name__ == "__main__":
    main()
