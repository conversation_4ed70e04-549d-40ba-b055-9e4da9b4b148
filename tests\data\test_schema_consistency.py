# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test schema consistency by running dataset validation.
Ensures all datasets conform to expected schemas.
"""

import pytest
import pandas as pd
import os
import logging
import subprocess
import sys

from dataset.schemas import match_schema
from dataset.feature_schemas import validate_features_for_draft_prediction

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_match_prediction_schema():
    """Test that the match prediction dataset conforms to schema."""
    
    dataset_path = "dataset/train_data/all_data_match_predict.csv"
    
    if not os.path.exists(dataset_path):
        pytest.skip("Match prediction dataset not found")
    
    # Load a sample of the dataset for testing
    df_sample = pd.read_csv(dataset_path, nrows=1000)
    logger.info(f"Loaded sample of {len(df_sample)} rows from match prediction dataset")
    
    try:
        # Validate against schema
        match_schema.validate(df_sample, lazy=True)
        logger.info("✅ Match prediction dataset schema validation passed")
    except Exception as e:
        pytest.fail(f"Match prediction dataset schema validation failed: {e}")

def test_draft_phase_schema():
    """Test that the draft-phase dataset has consistent schema."""
    
    dataset_path = "dataset/train_data/all_data_draft_phase.csv"
    
    if not os.path.exists(dataset_path):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv(dataset_path)
    logger.info(f"Loaded draft-phase dataset with {len(df)} rows and {len(df.columns)} columns")
    
    # Test basic schema requirements
    required_columns = ['match_id', 'radiant_win']
    for col in required_columns:
        assert col in df.columns, f"Required column missing: {col}"
    
    # Test hero ID columns
    hero_id_columns = [col for col in df.columns if 'hero_id' in col]
    assert len(hero_id_columns) >= 10, f"Expected at least 10 hero ID columns, got {len(hero_id_columns)}"
    
    # Test data types
    assert df['match_id'].dtype in ['int64', 'int32'], f"match_id should be integer, got {df['match_id'].dtype}"
    assert df['radiant_win'].dtype in ['int64', 'int32', 'bool'], f"radiant_win should be integer or bool, got {df['radiant_win'].dtype}"
    
    # Test hero ID data types
    for col in hero_id_columns:
        assert df[col].dtype in ['int64', 'int32'], f"Hero ID column {col} should be integer, got {df[col].dtype}"
    
    # Test value ranges
    assert df['radiant_win'].isin([0, 1, True, False]).all(), "radiant_win should only contain 0/1 or True/False"
    
    # Test hero ID ranges (should be positive integers)
    for col in hero_id_columns:
        non_null_values = df[col].dropna()
        if len(non_null_values) > 0:
            assert (non_null_values >= 0).all(), f"Hero ID column {col} contains negative values"
            assert (non_null_values <= 300).all(), f"Hero ID column {col} contains unrealistic values (>300)"
    
    logger.info("✅ Draft-phase dataset schema validation passed")

def test_dataset_validation_script():
    """Test that the dataset validation script runs successfully."""
    
    validation_script = "dataset/validate_training_data.py"
    
    if not os.path.exists(validation_script):
        pytest.skip("Dataset validation script not found")
    
    # Check if the main dataset exists
    main_dataset = "dataset/train_data/all_data_match_predict.csv"
    if not os.path.exists(main_dataset):
        pytest.skip("Main dataset not found - cannot run validation script")
    
    try:
        # Run the validation script
        result = subprocess.run([
            sys.executable, validation_script
        ], capture_output=True, text=True, timeout=60)
        
        logger.info(f"Validation script exit code: {result.returncode}")
        if result.stdout:
            logger.info(f"Validation script output: {result.stdout}")
        if result.stderr:
            logger.warning(f"Validation script errors: {result.stderr}")
        
        # The script should exit with code 0 for success
        assert result.returncode == 0, f"Dataset validation script failed with exit code {result.returncode}"
        
        logger.info("✅ Dataset validation script test passed")
        
    except subprocess.TimeoutExpired:
        pytest.fail("Dataset validation script timed out")
    except Exception as e:
        pytest.fail(f"Error running dataset validation script: {e}")

def test_feature_validation_consistency():
    """Test that feature validation is consistent across datasets."""
    
    # Test draft-phase dataset
    draft_path = "dataset/train_data/all_data_draft_phase.csv"
    if os.path.exists(draft_path):
        df_draft = pd.read_csv(draft_path)
        is_valid, forbidden = validate_features_for_draft_prediction(df_draft.columns.tolist())
        assert is_valid, f"Draft dataset failed feature validation: {forbidden}"
        logger.info("Draft dataset feature validation passed")
    
    # Test that full dataset would fail draft validation (as expected)
    full_path = "dataset/train_data/all_data_match_predict.csv"
    if os.path.exists(full_path):
        df_full = pd.read_csv(full_path, nrows=0)  # Just get columns
        is_valid, forbidden = validate_features_for_draft_prediction(df_full.columns.tolist())
        assert not is_valid, "Full dataset should fail draft validation (contains post-match features)"
        assert len(forbidden) > 0, "Full dataset should have forbidden features for draft prediction"
        logger.info(f"Full dataset correctly failed draft validation: {len(forbidden)} forbidden features")
    
    logger.info("✅ Feature validation consistency test passed")

def test_data_quality_checks():
    """Test basic data quality across datasets."""
    
    datasets_to_check = [
        ("dataset/train_data/all_data_match_predict.csv", "full"),
        ("dataset/train_data/all_data_draft_phase.csv", "draft")
    ]
    
    for dataset_path, dataset_type in datasets_to_check:
        if not os.path.exists(dataset_path):
            logger.warning(f"Dataset not found: {dataset_path}")
            continue
        
        df = pd.read_csv(dataset_path)
        logger.info(f"Checking data quality for {dataset_type} dataset: {len(df)} rows")
        
        # Check for completely empty datasets
        assert len(df) > 0, f"{dataset_type} dataset is empty"
        
        # Check for reasonable number of columns
        assert len(df.columns) >= 10, f"{dataset_type} dataset has too few columns: {len(df.columns)}"
        
        # Check for match_id column
        assert 'match_id' in df.columns, f"{dataset_type} dataset missing match_id column"
        
        # Check for target column
        assert 'radiant_win' in df.columns, f"{dataset_type} dataset missing radiant_win column"
        
        # Check for duplicate match IDs
        duplicate_matches = df['match_id'].duplicated().sum()
        duplicate_ratio = duplicate_matches / len(df)
        assert duplicate_ratio < 0.1, f"{dataset_type} dataset has too many duplicate match IDs: {duplicate_ratio:.2%}"
        
        # Check target distribution
        target_dist = df['radiant_win'].value_counts(normalize=True)
        logger.info(f"{dataset_type} target distribution: {target_dist.to_dict()}")
        
        # Should be reasonably balanced (not more than 70% either way)
        for value, proportion in target_dist.items():
            assert proportion <= 0.70, f"{dataset_type} dataset too imbalanced: {value}={proportion:.2%}"
        
        # Check for excessive missing values
        missing_ratio = df.isnull().sum().sum() / (len(df) * len(df.columns))
        assert missing_ratio < 0.20, f"{dataset_type} dataset has too many missing values: {missing_ratio:.2%}"
        
        logger.info(f"✅ {dataset_type} dataset quality checks passed")

def test_column_naming_consistency():
    """Test that column naming follows consistent patterns."""
    
    draft_path = "dataset/train_data/all_data_draft_phase.csv"
    if not os.path.exists(draft_path):
        pytest.skip("Draft dataset not found")
    
    df = pd.read_csv(draft_path, nrows=0)  # Just get columns
    columns = df.columns.tolist()
    
    # Check hero ID column naming pattern
    hero_id_columns = [col for col in columns if 'hero_id' in col]
    
    expected_hero_patterns = []
    for team in ['radiant', 'dire']:
        for player in range(1, 6):
            expected_hero_patterns.append(f"{team}_player_{player}_hero_id")
    
    for pattern in expected_hero_patterns:
        assert pattern in hero_id_columns, f"Expected hero ID column not found: {pattern}"
    
    # Check that column names don't have unexpected characters
    for col in columns:
        assert not col.startswith(' '), f"Column name starts with space: '{col}'"
        assert not col.endswith(' '), f"Column name ends with space: '{col}'"
        assert '  ' not in col, f"Column name has double spaces: '{col}'"
    
    logger.info("✅ Column naming consistency test passed")

if __name__ == "__main__":
    # Run tests directly
    test_draft_phase_schema()
    test_feature_validation_consistency()
    test_data_quality_checks()
    test_column_naming_consistency()
    # test_match_prediction_schema()  # May be slow
    # test_dataset_validation_script()  # May be slow
