# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Generate draft-phase only dataset for tournament match prediction.
Uses only pre-game information available at draft time.
"""

import pandas as pd
import requests
import logging
from datetime import datetime, timezone
from structure.struct import Tournament
from structure.opendota import OpenDotaApi
from dataset.feature_schemas import validate_features_for_draft_prediction

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_hero_global_winrate(hero_id, api_key):
    """Get global professional winrate for a hero."""
    try:
        url = f"https://api.opendota.com/api/heroStats?api_key={api_key}"
        response = requests.get(url)
        response.raise_for_status()
        
        heroes = response.json()
        for hero in heroes:
            if hero["id"] == hero_id:
                if hero.get("pro_pick", 0) > 0:
                    return hero["pro_win"] / hero["pro_pick"]
                else:
                    return 0.5  # Default if no pro games
        
        return 0.5  # Default if hero not found
    except Exception as e:
        logger.warning(f"Error fetching hero winrate for {hero_id}: {e}")
        return 0.5

def get_player_hero_expertise(player_id, hero_id, api_key):
    """Get player's expertise with specific hero."""
    try:
        url = f"https://api.opendota.com/api/players/{player_id}/heroes"
        params = {'api_key': api_key}
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        hero_stats = response.json()
        
        for hero in hero_stats:
            if str(hero.get('hero_id')) == str(hero_id):
                games_played = int(hero.get('games', 0))
                wins = int(hero.get('win', 0))
                win_rate = wins / games_played if games_played > 0 else 0.5
                last_played = hero.get('last_played', 0)
                
                return {
                    'hero_games_played': games_played,
                    'hero_winrate': win_rate,
                    'last_played': last_played
                }
        
        # If hero not found in player stats, return defaults
        return {
            'hero_games_played': 0,
            'hero_winrate': 0.5,
            'last_played': 0
        }
        
    except Exception as e:
        logger.warning(f"Error fetching player {player_id} hero {hero_id} expertise: {e}")
        return {
            'hero_games_played': 0,
            'hero_winrate': 0.5,
            'last_played': 0
        }

def generate_draft_phase_dataset():
    """Generate dataset with only draft-phase features."""
    api = OpenDotaApi()
    dataset = []
    
    # Use same premium leagues as before
    premium_leagues = api.set_premium_leagues()
    
    # Focus on recent major tournaments for better data quality
    target_leagues = [
        "ESL One Kuala Lumpur powered by Intel",
        "BetBoom Dacha Dubai 2024", 
        "DreamLeague Season 22 powered by Intel",
        "ESL One Birmingham 2024 Powered by Intel",
        "DreamLeague Season 23 powered by Intel",
        "Riyadh Masters 2024 at Esports World Cup",
        "The International 2024",
        "PGL Wallachia 2024 Season 1",
        "The International 2023",
        "Riyadh Masters 2023 by Gamers8",
        "DreamLeague Season 21 powered by Intel",
        "The Bali Major",
        "DreamLeague Season 20 powered by Intel",
        "ESL One The Berlin Major powered by Intel"
    ]
    
    for premium_league in premium_leagues:
        league_id = premium_league["leagueid"]
        league_name = premium_league["name"]
        
        if league_name in target_leagues:
            logger.info(f"Processing league: {league_name}")
            tournament = Tournament(league_id=league_id, name=league_name)
            tournament.get_league_matches()
            
            for match in tournament.matches:
                radiant_team = match.radiant_team
                dire_team = match.dire_team
                
                # Ensure we have 5 players on each team
                if len(radiant_team.players) == 5 and len(dire_team.players) == 5:
                    try:
                        # Create match data with only draft-phase features
                        match_data = {
                            "match_id": match.match_id,
                            "radiant_win": match.radiant_win,
                            "match_start_time": datetime.now(timezone.utc).isoformat(),  # Would need actual timestamp
                        }
                        
                        # Add radiant team draft information
                        for i, player in enumerate(radiant_team.players):
                            match_data[f"radiant_player_{i + 1}_hero_id"] = player.hero.hero_id
                            
                            # Get player-hero expertise
                            expertise = get_player_hero_expertise(
                                player.account_id, 
                                player.hero.hero_id, 
                                api.api_key
                            )
                            match_data[f"radiant_player_{i + 1}_hero_games_played"] = expertise['hero_games_played']
                            match_data[f"radiant_player_{i + 1}_hero_winrate"] = expertise['hero_winrate']
                            match_data[f"radiant_player_{i + 1}_last_played"] = expertise['last_played']
                            
                            # Get global hero winrate
                            global_wr = get_hero_global_winrate(player.hero.hero_id, api.api_key)
                            match_data[f"radiant_player_{i + 1}_hero_global_winrate"] = global_wr
                        
                        # Add dire team draft information
                        for i, player in enumerate(dire_team.players):
                            match_data[f"dire_player_{i + 1}_hero_id"] = player.hero.hero_id
                            
                            # Get player-hero expertise
                            expertise = get_player_hero_expertise(
                                player.account_id,
                                player.hero.hero_id,
                                api.api_key
                            )
                            match_data[f"dire_player_{i + 1}_hero_games_played"] = expertise['hero_games_played']
                            match_data[f"dire_player_{i + 1}_hero_winrate"] = expertise['hero_winrate']
                            match_data[f"dire_player_{i + 1}_last_played"] = expertise['last_played']
                            
                            # Get global hero winrate
                            global_wr = get_hero_global_winrate(player.hero.hero_id, api.api_key)
                            match_data[f"dire_player_{i + 1}_hero_global_winrate"] = global_wr
                        
                        # Validate no forbidden features are present
                        is_valid, forbidden = validate_features_for_draft_prediction(match_data.keys())
                        if not is_valid:
                            logger.warning(f"Match {match.match_id} contains forbidden features: {forbidden}")
                            continue
                        
                        dataset.append(match_data)
                        
                        if len(dataset) % 100 == 0:
                            logger.info(f"Processed {len(dataset)} matches so far...")
                            
                    except Exception as e:
                        logger.error(f"Error processing match {match.match_id}: {e}")
                        continue
            
            # Save data for this league
            if dataset:
                df = pd.DataFrame(dataset)
                filename = f"draft_phase_matches_{league_name.replace(' ', '_')}.csv"
                df.to_csv(filename, index=False)
                logger.info(f"Saved {len(dataset)} matches to {filename}")
    
    # Create combined dataset
    if dataset:
        df = pd.DataFrame(dataset)
        df.to_csv("dataset/train_data/all_data_draft_phase.csv", index=False)
        logger.info(f"Draft phase dataset generated with {len(dataset)} matches")
        logger.info(f"Features: {df.columns.tolist()}")
        
        # Validate final dataset
        is_valid, forbidden = validate_features_for_draft_prediction(df.columns.tolist())
        if is_valid:
            logger.info("✅ Dataset validation passed - no post-match features detected")
        else:
            logger.error(f"❌ Dataset validation failed - forbidden features found: {forbidden}")
    else:
        logger.error("No matches were processed successfully")

if __name__ == "__main__":
    generate_draft_phase_dataset()
