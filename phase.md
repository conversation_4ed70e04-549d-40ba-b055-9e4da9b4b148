## PHASE 1 Baseline Re-evaluation

• Load the original full-data model `xgb_model.pkl`.\
• Score it on a *strict* hold-out set that contains __only draft-time information__ (drop in-game stats).\
→ This gives a “floor” accuracy/AUC that we must surpass.\
• Log calibration (Brier score / reliability plot) to see if probabilities are meaningful.

Tests

1. `tests/baseline/test_full_model_on_draft_only.py`\
   – Ensure the model loads and produces predictions with no leakage columns.\
   – Assert accuracy > 0.50 (better than coin-flip) and log_loss below a loose ceiling.

---

## PHASE 2 Draft-Only Model v1 (current improvements)

• Use `ml/create_model_draft_phase.py` to retrain with hero-ID + player-expertise + global win-rate features.\
• Compare against Phase 1 on the same hold-out.\
• Add time-based cross-validation (GroupKFold by tournament or month) to ensure generalisation.

Tests\
2. `tests/model_v1/test_metrics.py`\
– Train on small deterministic subset (seeded).\
– Assert __relative__ gain over baseline: e.g. Accuracy ≥ Baseline + 0.02 __or__ Log<PERSON>oss ≤ Baseline − 0.02.\
– Assert no forbidden columns slip into the training matrix (`validate_features_for_draft_prediction`).\
3. `tests/model_v1/test_calibration.py`\
– Compute Brier score; must not exceed baseline by more than 5 %.\
4. `tests/model_v1/test_feature_importance_snapshot.py`\
– Save top-20 feature list; fail if it suddenly becomes empty (signal leakage or prep bug).

---

## PHASE 3 Feature-Enhanced / Ensemble Model

• Implement aggregations, synergy, team meta, optional NN ensemble (per plan).\
• Retrain (`xgb_model_draft_v2.pkl`).

Tests\
5. `tests/model_v2/test_metrics.py` (same thresholds but expect another incremental gain).\
6. `tests/model_v2/test_statistical_significance.py`\
– Use bootstrap or McNemar test to ensure improvement is statistically significant (p < 0.05).\
7. `tests/model_v2/test_ablation.py`\
– Drop each new feature group (aggregations, synergy, meta) and ensure overall metric declines ⇒ proves usefulness.

---

## Shared Integrity & CI Checks

A. `tests/data/test_pipeline_purity.py`\
– Walk every CSV in `dataset/train_data/`; fail if any forbidden token (`kills`, `xp_per_min`, …) appears.\
B. `tests/data/test_schema_consistency.py`\
– Load dataset → run `validate_training_data.py`; assert pass.\
C. `tests/inference/test_bot_endpoint.py`\
– Mock a draft; ensure struct.Match.get_draft_data_for_prediction → model.predict returns probability in (0,1).

CI Threshold Management\
• Store baseline metrics in `tests/expected/baseline_metrics.json`.\
• Phase 2 and 3 tests compare against those JSON values so we track regressions.\
• Use environment variable `ALLOW_METRIC_DROP=False` in CI to gate merges.

Key Metrics to Report\
– Accuracy\
– Log Loss (primary for probabilistic tasks)\
– Brier score (calibration)\
– ROC-AUC / PR-AUC\
– Calibration curve buckets (visual but can also assert ECE < threshold)

With this structure we can:

1. Prove the old model is inadequate in a leakage-free setting.
2. Quantitatively verify each improvement step.
3. Guard against accidental leakage or regression via automated tests.
