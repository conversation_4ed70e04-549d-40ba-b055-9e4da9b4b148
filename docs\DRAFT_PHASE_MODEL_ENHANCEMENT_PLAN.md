# Draft-Phase Model Enhancement Plan

This document outlines a step-by-step plan for enriching and validating the pre-game (draft-phase) match-prediction model while ensuring pipeline purity and automated tests.

## 1. Define New Features
1. **Team-level aggregations**  
   - Compute mean, standard deviation, minimum, and maximum across the five players for each of:  
     - `hero_winrate`  
     - `hero_games_played`  
     - `last_played` (recency proxy)  
     - `hero_global_winrate`  
2. **Pairwise synergy features (optional)**  
   - Fetch historic team winrates for hero pairs via OpenDota’s `/heroes/combos` endpoint  
   - Add average synergy score per team  
3. **Team metadata**  
   - `team_id` (categorical)  
   - Team form: winrate over last N pro matches  
   - `patch_version` and `tournament_tier` labels  
   - Time since patch release (meta maturity)

## 2. Extend Dataset Generator
- **File**: `dataset/generate_dataset_draft_phase.py`  
- After per-player draft fields, compute and append team aggregations (`mean`, `std`, `min`, `max`).  
- If used, fetch and append synergy features.  
- Populate team metadata columns (team form, patch, tier).

## 3. Update Feature Schema
- **File**: `dataset/feature_schemas.py`  
- Add new aggregation and metadata column names to `DRAFT_PHASE_FEATURES`.  
- Retain `FORBIDDEN_FEATURES` unchanged to guard against leakage.

## 4. Adapt Data Preparation
- **File**: `structure/helpers.py`  
- In `prepare_draft_phase_data()`, detect the new numerical aggregation columns and include them in scaling.  
- Decide encoding strategy for categoricals (team_id, patch_version).  
- Update whitelist validation to include new columns.

## 5. Retrain Draft-Phase Model
- **File**: `ml/create_model_draft_phase.py`  
- Log inclusion of new features.  
- Optionally implement a stacking ensemble:  
  - Train a small feed-forward neural network on the same inputs (e.g. PyTorch MLP)  
  - Stack or average its predictions with XGBoost output  

## 6. Write Automated Tests
1. **Pipeline Purity Tests** (`tests/test_dataset_purity.py`)  
   - Load synthetic draft-phase CSV; assert `validate_features_for_draft_prediction()` returns `True`.  
   - Inject a forbidden feature; assert validation fails.  
2. **Feature Generation Tests** (`tests/test_generate_dataset_draft_phase.py`)  
   - Mock a minimal `Tournament` with two 5-player teams.  
   - Run generator; assert new aggregation columns exist and values are correct.  
3. **Model Improvement Smoke Tests** (`tests/test_model_draft_phase.py`)  
   - Train on a small fixed sample before/after adding new features.  
   - Assert performance (accuracy/AUC) does not degrade and shows marginal improvement.  
   - Use fixed random seed for reproducibility.

## 7. Continuous Integration
- Add new tests to the existing `pytest` suite.  
- Ensure `pre-commit` and coverage checks include pipeline purity and model tests.

## 8. Deployment & Monitoring
- Publish retrained model artifact (e.g. `xgb_model_draft_v2.pkl`).  
- Update bot configuration to reference the new model.  
- Monitor live prediction logs for drift and schedule periodic retraining.
