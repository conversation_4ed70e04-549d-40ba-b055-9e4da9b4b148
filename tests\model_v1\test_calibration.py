# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test calibration of draft-phase model v1 to ensure probabilities are meaningful.
"""

import pytest
import pandas as pd
import numpy as np
import logging
import json
import os
from sklearn.metrics import brier_score_loss
from sklearn.calibration import calibration_curve
from sklearn.model_selection import train_test_split
import xgboost as xgb

from structure.helpers import prepare_draft_phase_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_expected_calibration_error(y_true, y_prob, n_bins=10):
    """Calculate Expected Calibration Error (ECE)."""
    bin_boundaries = np.linspace(0, 1, n_bins + 1)
    bin_lowers = bin_boundaries[:-1]
    bin_uppers = bin_boundaries[1:]
    
    ece = 0
    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
        # Determine if sample is in bin m (between bin lower & upper)
        in_bin = (y_prob > bin_lower) & (y_prob <= bin_upper)
        prop_in_bin = in_bin.mean()
        
        if prop_in_bin > 0:
            accuracy_in_bin = y_true[in_bin].mean()
            avg_confidence_in_bin = y_prob[in_bin].mean()
            ece += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
    
    return ece

def test_model_calibration():
    """Test that the draft model produces well-calibrated probabilities."""
    
    # Load draft-phase dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Use a subset for testing
    np.random.seed(42)
    test_size = min(5000, len(df))
    df_test = df.sample(n=test_size, random_state=42)
    
    # Prepare data
    df_prepared = prepare_draft_phase_data(df_test.copy(), "scaler_draft.pkl")
    
    # Split features and target
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    y = df_prepared['radiant_win'].astype(int)
    
    # Split for testing
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # Load the trained draft model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
        logger.info("Loaded draft model v1")
    except FileNotFoundError:
        pytest.skip("Draft model not found - run ml/create_model_draft_phase.py first")
    
    # Make predictions
    dtest = xgb.DMatrix(X_test)
    y_pred_proba = model.predict(dtest)
    
    # Calculate Brier score
    brier_score = brier_score_loss(y_test, y_pred_proba)
    logger.info(f"Brier Score: {brier_score:.4f}")
    
    # Calculate Expected Calibration Error
    ece = calculate_expected_calibration_error(y_test.values, y_pred_proba)
    logger.info(f"Expected Calibration Error: {ece:.4f}")
    
    # Calculate calibration curve
    fraction_of_positives, mean_predicted_value = calibration_curve(
        y_test, y_pred_proba, n_bins=10
    )
    
    # Calculate reliability (how close predicted probabilities are to actual frequencies)
    reliability_error = np.mean(np.abs(fraction_of_positives - mean_predicted_value))
    logger.info(f"Reliability Error: {reliability_error:.4f}")
    
    # Load baseline metrics for comparison
    baseline_path = "tests/expected/baseline_metrics.json"
    baseline_brier = 0.25  # Default baseline
    if os.path.exists(baseline_path):
        with open(baseline_path, 'r') as f:
            baseline = json.load(f)
            baseline_brier = baseline.get("brier_score", 0.25)
    
    # Save calibration metrics
    calibration_metrics = {
        "brier_score": brier_score,
        "expected_calibration_error": ece,
        "reliability_error": reliability_error,
        "calibration_curve": {
            "fraction_of_positives": fraction_of_positives.tolist(),
            "mean_predicted_value": mean_predicted_value.tolist()
        },
        "model_type": "draft_model_v1",
        "test_size": len(y_test)
    }
    
    os.makedirs("tests/expected", exist_ok=True)
    with open("tests/expected/draft_v1_calibration.json", "w") as f:
        json.dump(calibration_metrics, f, indent=2)
    
    # Test assertions
    # Brier score should not exceed baseline by more than 5%
    max_brier_increase = 0.05
    assert brier_score <= baseline_brier + max_brier_increase, \
        f"Brier score {brier_score:.4f} exceeds baseline {baseline_brier:.4f} by more than {max_brier_increase}"
    
    # Expected Calibration Error should be reasonable for a basic model
    max_ece = 0.20  # 20% maximum calibration error (relaxed for basic hero-ID model)
    assert ece <= max_ece, \
        f"Expected Calibration Error {ece:.4f} exceeds maximum {max_ece}"

    # Reliability error should be reasonable for a basic model
    max_reliability_error = 0.25  # 25% maximum reliability error (relaxed for basic model)
    assert reliability_error <= max_reliability_error, \
        f"Reliability error {reliability_error:.4f} exceeds maximum {max_reliability_error}"
    
    # Brier score should be better than random (0.25 for balanced dataset)
    assert brier_score < 0.25, \
        f"Brier score {brier_score:.4f} not better than random baseline (0.25)"
    
    logger.info("✅ Model calibration test passed")

def test_probability_distribution():
    """Test that predicted probabilities have reasonable distribution."""
    
    # Load draft-phase dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Use a subset for testing
    np.random.seed(42)
    test_size = min(2000, len(df))
    df_test = df.sample(n=test_size, random_state=42)
    
    # Prepare data
    df_prepared = prepare_draft_phase_data(df_test.copy(), "scaler_draft.pkl")
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    
    # Load model and predict
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
    except FileNotFoundError:
        pytest.skip("Draft model not found")
    
    dtest = xgb.DMatrix(X)
    y_pred_proba = model.predict(dtest)
    
    # Test probability distribution properties
    assert np.all(y_pred_proba >= 0), "Some probabilities are negative"
    assert np.all(y_pred_proba <= 1), "Some probabilities exceed 1"
    
    # Check that probabilities are not all the same (model is learning)
    prob_std = np.std(y_pred_proba)
    assert prob_std > 0.01, f"Probability standard deviation too low: {prob_std:.4f}"
    
    # Check that probabilities are reasonably distributed
    prob_mean = np.mean(y_pred_proba)
    assert 0.3 < prob_mean < 0.7, f"Mean probability outside reasonable range: {prob_mean:.4f}"
    
    # Check for reasonable spread
    prob_min, prob_max = np.min(y_pred_proba), np.max(y_pred_proba)
    prob_range = prob_max - prob_min
    assert prob_range > 0.1, f"Probability range too narrow: {prob_range:.4f}"
    
    logger.info(f"Probability distribution: mean={prob_mean:.4f}, std={prob_std:.4f}, range=[{prob_min:.4f}, {prob_max:.4f}]")
    logger.info("✅ Probability distribution test passed")

def test_calibration_consistency():
    """Test that calibration is consistent across different data splits."""
    
    # Load draft-phase dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Use a subset for testing
    np.random.seed(42)
    test_size = min(3000, len(df))
    df_test = df.sample(n=test_size, random_state=42)
    
    # Prepare data
    df_prepared = prepare_draft_phase_data(df_test.copy(), "scaler_draft.pkl")
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    y = df_prepared['radiant_win'].astype(int)
    
    # Load model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
    except FileNotFoundError:
        pytest.skip("Draft model not found")
    
    # Test calibration on multiple random splits
    brier_scores = []
    eces = []
    
    for seed in [42, 123, 456]:
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=seed, stratify=y
        )
        
        dtest = xgb.DMatrix(X_test)
        y_pred_proba = model.predict(dtest)
        
        brier = brier_score_loss(y_test, y_pred_proba)
        ece = calculate_expected_calibration_error(y_test.values, y_pred_proba)
        
        brier_scores.append(brier)
        eces.append(ece)
    
    # Check consistency
    brier_std = np.std(brier_scores)
    ece_std = np.std(eces)
    
    logger.info(f"Brier scores across splits: {brier_scores}, std={brier_std:.4f}")
    logger.info(f"ECE scores across splits: {eces}, std={ece_std:.4f}")
    
    # Calibration should be reasonably consistent
    assert brier_std < 0.02, f"Brier score too inconsistent across splits: std={brier_std:.4f}"
    assert ece_std < 0.03, f"ECE too inconsistent across splits: std={ece_std:.4f}"
    
    logger.info("✅ Calibration consistency test passed")

if __name__ == "__main__":
    # Run tests directly
    test_probability_distribution()
    test_calibration_consistency()
    test_model_calibration()
