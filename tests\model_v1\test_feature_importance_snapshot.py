# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test feature importance snapshot for draft-phase model v1.
Ensures feature importance is stable and meaningful.
"""

import pytest
import pandas as pd
import numpy as np
import logging
import json
import os
import xgboost as xgb

from structure.helpers import prepare_draft_phase_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_feature_importance_snapshot():
    """Test that feature importance is captured and reasonable."""
    
    # Load the trained draft model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
        logger.info("Loaded draft model v1")
    except FileNotFoundError:
        pytest.skip("Draft model not found - run ml/create_model_draft_phase.py first")
    
    # Get feature importance
    importance_weight = model.get_score(importance_type='weight')
    importance_gain = model.get_score(importance_type='gain')
    importance_cover = model.get_score(importance_type='cover')
    
    logger.info(f"Feature importance (weight): {importance_weight}")
    logger.info(f"Feature importance (gain): {importance_gain}")
    
    # Create comprehensive importance snapshot
    all_features = set(importance_weight.keys()) | set(importance_gain.keys()) | set(importance_cover.keys())
    
    importance_snapshot = {}
    for feature in all_features:
        importance_snapshot[feature] = {
            'weight': importance_weight.get(feature, 0),
            'gain': importance_gain.get(feature, 0.0),
            'cover': importance_cover.get(feature, 0.0)
        }
    
    # Sort by weight (most commonly used importance metric)
    sorted_features = sorted(importance_snapshot.items(), 
                           key=lambda x: x[1]['weight'], reverse=True)
    
    # Get top 20 features
    top_20_features = dict(sorted_features[:20])
    
    logger.info("Top 20 Most Important Features (by weight):")
    for i, (feature, scores) in enumerate(sorted_features[:20], 1):
        logger.info(f"{i:2d}. {feature}: weight={scores['weight']}, gain={scores['gain']:.4f}")
    
    # Save feature importance snapshot
    snapshot_data = {
        "model_type": "draft_model_v1",
        "timestamp": pd.Timestamp.now().isoformat(),
        "top_20_features": top_20_features,
        "all_features": importance_snapshot,
        "feature_count": len(all_features),
        "total_weight": sum(importance_weight.values()),
        "total_gain": sum(importance_gain.values())
    }
    
    os.makedirs("tests/expected", exist_ok=True)
    with open("tests/expected/draft_v1_feature_importance.json", "w") as f:
        json.dump(snapshot_data, f, indent=2)
    
    # Test assertions
    # Must have features (not empty)
    assert len(all_features) > 0, "Feature importance list is empty - possible model training issue"
    
    # Should have exactly 10 features for basic draft model (hero IDs)
    expected_feature_count = 10
    assert len(all_features) == expected_feature_count, \
        f"Expected {expected_feature_count} features, got {len(all_features)}: {list(all_features)}"
    
    # All features should be hero ID features
    for feature in all_features:
        assert 'hero_id' in feature, f"Non-hero-ID feature found: {feature}"
    
    # Check that we have both radiant and dire hero features
    radiant_features = [f for f in all_features if f.startswith('radiant_')]
    dire_features = [f for f in all_features if f.startswith('dire_')]
    
    assert len(radiant_features) == 5, f"Expected 5 radiant hero features, got {len(radiant_features)}"
    assert len(dire_features) == 5, f"Expected 5 dire hero features, got {len(dire_features)}"
    
    # Feature importance values should be reasonable
    max_weight = max(importance_weight.values())
    min_weight = min(importance_weight.values())
    
    assert max_weight > 0, "Maximum feature weight should be positive"
    assert min_weight >= 0, "All feature weights should be non-negative"
    
    # No single feature should dominate completely
    total_weight = sum(importance_weight.values())
    max_weight_ratio = max_weight / total_weight if total_weight > 0 else 0
    
    assert max_weight_ratio < 0.8, \
        f"Single feature dominates too much: {max_weight_ratio:.2%} of total importance"
    
    logger.info(f"✅ Feature importance snapshot test passed: {len(all_features)} features captured")

def test_feature_importance_stability():
    """Test that feature importance is stable across different data samples."""
    
    # Load draft-phase dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft-phase dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Load the trained model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
    except FileNotFoundError:
        pytest.skip("Draft model not found")
    
    # Get current feature importance
    current_importance = model.get_score(importance_type='weight')
    
    # Check if we have a previous snapshot to compare against
    snapshot_path = "tests/expected/draft_v1_feature_importance.json"
    if os.path.exists(snapshot_path):
        with open(snapshot_path, 'r') as f:
            previous_snapshot = json.load(f)
        
        previous_importance = {k: v['weight'] for k, v in previous_snapshot['all_features'].items()}
        
        # Compare feature rankings
        current_ranking = sorted(current_importance.items(), key=lambda x: x[1], reverse=True)
        previous_ranking = sorted(previous_importance.items(), key=lambda x: x[1], reverse=True)
        
        # Get top 5 features from each
        current_top5 = [f[0] for f in current_ranking[:5]]
        previous_top5 = [f[0] for f in previous_ranking[:5]]
        
        # At least 3 out of top 5 should be consistent
        overlap = len(set(current_top5) & set(previous_top5))
        min_overlap = 3
        
        logger.info(f"Current top 5: {current_top5}")
        logger.info(f"Previous top 5: {previous_top5}")
        logger.info(f"Overlap: {overlap}/{min_overlap}")
        
        assert overlap >= min_overlap, \
            f"Feature importance too unstable: only {overlap}/{min_overlap} top features consistent"
        
        logger.info("✅ Feature importance stability test passed")
    else:
        logger.info("No previous snapshot found - skipping stability test")

def test_hero_id_feature_coverage():
    """Test that all expected hero ID features are present and have importance."""
    
    # Load the trained model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
    except FileNotFoundError:
        pytest.skip("Draft model not found")
    
    # Get feature importance
    importance = model.get_score(importance_type='weight')
    
    # Expected hero ID features
    expected_features = []
    for team in ['radiant', 'dire']:
        for player in range(1, 6):
            expected_features.append(f"{team}_player_{player}_hero_id")
    
    # Check that all expected features are present
    missing_features = []
    for feature in expected_features:
        if feature not in importance:
            missing_features.append(feature)
    
    assert len(missing_features) == 0, \
        f"Missing expected hero ID features: {missing_features}"
    
    # Check that all features have non-zero importance
    zero_importance_features = []
    for feature in expected_features:
        if importance.get(feature, 0) == 0:
            zero_importance_features.append(feature)
    
    # Allow some features to have zero importance, but not all
    max_zero_features = 2  # Allow up to 2 features to have zero importance
    assert len(zero_importance_features) <= max_zero_features, \
        f"Too many features with zero importance: {zero_importance_features}"
    
    logger.info(f"✅ Hero ID feature coverage test passed: {len(expected_features)} features checked")

def test_feature_importance_distribution():
    """Test that feature importance is reasonably distributed."""
    
    # Load the trained model
    try:
        model = xgb.Booster()
        model.load_model("xgb_model_draft.pkl")
    except FileNotFoundError:
        pytest.skip("Draft model not found")
    
    # Get feature importance
    importance = model.get_score(importance_type='weight')
    importance_values = list(importance.values())
    
    # Calculate distribution statistics
    mean_importance = np.mean(importance_values)
    std_importance = np.std(importance_values)
    min_importance = np.min(importance_values)
    max_importance = np.max(importance_values)
    
    logger.info(f"Feature importance distribution:")
    logger.info(f"  Mean: {mean_importance:.2f}")
    logger.info(f"  Std:  {std_importance:.2f}")
    logger.info(f"  Min:  {min_importance:.2f}")
    logger.info(f"  Max:  {max_importance:.2f}")
    
    # Test distribution properties
    # Standard deviation should not be too high (features shouldn't be too unbalanced)
    cv = std_importance / mean_importance if mean_importance > 0 else float('inf')
    max_cv = 1.0  # Coefficient of variation should be reasonable
    
    assert cv <= max_cv, \
        f"Feature importance too unbalanced: CV={cv:.2f} > {max_cv}"
    
    # No feature should have more than 50% of total importance
    total_importance = sum(importance_values)
    max_ratio = max_importance / total_importance if total_importance > 0 else 0
    
    assert max_ratio <= 0.5, \
        f"Single feature has too much importance: {max_ratio:.2%} of total"
    
    logger.info("✅ Feature importance distribution test passed")

if __name__ == "__main__":
    # Run tests directly
    test_hero_id_feature_coverage()
    test_feature_importance_distribution()
    test_feature_importance_stability()
    test_feature_importance_snapshot()
