# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test loading and using the trained LSTM model.
"""

import torch
import numpy as np
import logging
import sys
import os
from gensim.models import KeyedVectors
import pickle

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml.create_model_lstm import CBOWLSTMModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_trained_model(model_path: str = "models/lstm_model.pth") -> CBOWLSTMModel:
    """Load the trained LSTM model."""
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Create model with same configuration
        model = CBOWLSTMModel(
            embedding_dim=150,
            lstm_hidden_size=150,
            dropout_rate=0.3
        )
        
        # Load state dict
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        logger.info(f"✅ Successfully loaded LSTM model from {model_path}")
        return model
        
    except Exception as e:
        logger.error(f"❌ Failed to load LSTM model: {e}")
        raise

def test_model_prediction():
    """Test the LSTM model with sample data."""
    logger.info("Testing LSTM model prediction")
    
    # Load model and embeddings
    model = load_trained_model()
    embeddings = KeyedVectors.load("models/hero_embeddings.kv")
    
    # Test with a sample pick sequence
    # This is from our test data: [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
    test_sequence = [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
    
    logger.info(f"Testing with pick sequence: {test_sequence}")
    
    # Convert to embeddings
    embedded_sequence = []
    for hero_id in test_sequence:
        hero_str = str(hero_id)
        if hero_str in embeddings.key_to_index:
            embedded_sequence.append(embeddings[hero_str])
        else:
            embedded_sequence.append(np.zeros(embeddings.vector_size))
            logger.warning(f"Hero {hero_id} not found in embeddings, using zero vector")
    
    embedded_sequence = np.array(embedded_sequence)
    
    # Split into Radiant and Dire picks
    radiant_picks = embedded_sequence[:5]  # First 5 picks
    dire_picks = embedded_sequence[5:]     # Last 5 picks
    
    # Convert to tensors and add batch dimension
    radiant_tensor = torch.FloatTensor(radiant_picks).unsqueeze(0)  # Shape: (1, 5, 150)
    dire_tensor = torch.FloatTensor(dire_picks).unsqueeze(0)        # Shape: (1, 5, 150)
    
    # Make prediction
    with torch.no_grad():
        prediction = model(radiant_tensor, dire_tensor)
        probability = prediction.item()
        predicted_winner = "Radiant" if probability > 0.5 else "Dire"
    
    logger.info(f"Prediction: {probability:.4f}")
    logger.info(f"Predicted winner: {predicted_winner}")
    logger.info(f"Confidence: {abs(probability - 0.5) * 2:.4f}")
    
    return probability

def test_multiple_predictions():
    """Test the model with multiple sample sequences."""
    logger.info("Testing LSTM model with multiple sequences")
    
    # Load model and embeddings
    model = load_trained_model()
    embeddings = KeyedVectors.load("models/hero_embeddings.kv")
    
    # Sample sequences from our test data
    test_sequences = [
        [31, 138, 121, 2, 56, 114, 137, 52, 8, 90],    # Expected: Dire win (0)
        [74, 5, 22, 99, 27, 35, 49, 44, 120, 10],      # Expected: Dire win (0)
        [101, 19, 7, 9, 104, 95, 135, 48, 39, 22],     # Expected: Radiant win (1)
    ]
    
    expected_results = [0, 0, 1]  # Actual results from our data
    
    for i, (sequence, expected) in enumerate(zip(test_sequences, expected_results)):
        logger.info(f"\nTest {i+1}: {sequence}")
        
        # Convert to embeddings
        embedded_sequence = []
        for hero_id in sequence:
            hero_str = str(hero_id)
            if hero_str in embeddings.key_to_index:
                embedded_sequence.append(embeddings[hero_str])
            else:
                embedded_sequence.append(np.zeros(embeddings.vector_size))
        
        embedded_sequence = np.array(embedded_sequence)
        
        # Split and convert to tensors
        radiant_picks = torch.FloatTensor(embedded_sequence[:5]).unsqueeze(0)
        dire_picks = torch.FloatTensor(embedded_sequence[5:]).unsqueeze(0)
        
        # Make prediction
        with torch.no_grad():
            prediction = model(radiant_picks, dire_picks)
            probability = prediction.item()
            predicted_class = 1 if probability > 0.5 else 0
        
        expected_winner = "Radiant" if expected == 1 else "Dire"
        predicted_winner = "Radiant" if predicted_class == 1 else "Dire"
        correct = "✅" if predicted_class == expected else "❌"
        
        logger.info(f"  Expected: {expected_winner} ({expected})")
        logger.info(f"  Predicted: {predicted_winner} ({probability:.4f}) {correct}")

def load_and_display_metrics():
    """Load and display training metrics."""
    try:
        with open("models/lstm_metrics.pkl", "rb") as f:
            metrics = pickle.load(f)
        
        logger.info("\nTraining Metrics:")
        logger.info(f"  Accuracy: {metrics['accuracy']:.4f}")
        logger.info(f"  ROC-AUC: {metrics['roc_auc']:.4f}")
        logger.info(f"  Log Loss: {metrics['log_loss']:.4f}")
        
        return metrics
        
    except Exception as e:
        logger.warning(f"Could not load metrics: {e}")
        return None

def test_lstm_model():
    """Main function to test the LSTM model."""
    logger.info("Starting LSTM model testing")
    
    # Test single prediction
    test_model_prediction()
    
    # Test multiple predictions
    test_multiple_predictions()
    
    # Display metrics
    load_and_display_metrics()
    
    logger.info("🎉 LSTM model testing completed successfully!")

if __name__ == "__main__":
    try:
        test_lstm_model()
    except Exception as e:
        logger.error(f"❌ Testing failed: {e}")
        raise
