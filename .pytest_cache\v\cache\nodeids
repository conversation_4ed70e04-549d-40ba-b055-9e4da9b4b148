["tests/baseline/test_full_model_on_draft_only.py::test_draft_dataset_creation", "tests/baseline/test_full_model_on_draft_only.py::test_full_model_on_draft_only", "tests/data/test_pipeline_purity.py::test_all_training_data_csvs", "tests/data/test_pipeline_purity.py::test_dataset_directory_structure", "tests/data/test_pipeline_purity.py::test_draft_phase_dataset_purity", "tests/data/test_pipeline_purity.py::test_feature_schema_validation", "tests/data/test_pipeline_purity.py::test_forbidden_patterns_coverage", "tests/data/test_schema_consistency.py::test_column_naming_consistency", "tests/data/test_schema_consistency.py::test_data_quality_checks", "tests/data/test_schema_consistency.py::test_dataset_validation_script", "tests/data/test_schema_consistency.py::test_draft_phase_schema", "tests/data/test_schema_consistency.py::test_feature_validation_consistency", "tests/data/test_schema_consistency.py::test_match_prediction_schema", "tests/enhancements/test_compute_player_hero_performance.py::TestCalculatePerformance::test_basic_performance_calculation", "tests/enhancements/test_compute_player_hero_performance.py::TestCalculatePerformance::test_data_integrity_validation", "tests/enhancements/test_compute_player_hero_performance.py::TestCalculatePerformance::test_missing_account_id_handling", "tests/enhancements/test_compute_player_hero_performance.py::TestCalculatePerformance::test_multiple_heroes_same_player", "tests/enhancements/test_compute_player_hero_performance.py::TestCalculatePerformance::test_result_dataframe_structure", "tests/enhancements/test_compute_player_hero_performance.py::TestCalculatePerformance::test_single_game_scenarios", "tests/enhancements/test_compute_team_performance.py::TestCalculateTeamPerformance::test_basic_team_performance_calculation", "tests/enhancements/test_compute_team_performance.py::TestCalculateTeamPerformance::test_head_to_head_calculation", "tests/enhancements/test_compute_team_performance.py::TestCalculateTeamPerformance::test_missing_team_ids_handling", "tests/enhancements/test_compute_team_performance.py::TestCalculateTeamPerformance::test_result_dataframe_structure", "tests/enhancements/test_compute_team_performance.py::TestCalculateTeamPerformance::test_single_team_scenarios", "tests/inference/test_bot_endpoint.py::test_different_hero_combinations", "tests/inference/test_bot_endpoint.py::test_draft_data_extraction", "tests/inference/test_bot_endpoint.py::test_draft_prediction_pipeline", "tests/inference/test_bot_endpoint.py::test_edge_cases", "tests/inference/test_bot_endpoint.py::test_prediction_consistency", "tests/inference/test_lstm_endpoint.py::test_endpoint_integration", "tests/inference/test_lstm_endpoint.py::test_invalid_sequence_length", "tests/inference/test_lstm_endpoint.py::test_mixed_known_unknown_heroes", "tests/inference/test_lstm_endpoint.py::test_multiple_predictions", "tests/inference/test_lstm_endpoint.py::test_prediction_consistency", "tests/inference/test_lstm_endpoint.py::test_predictor_initialization", "tests/inference/test_lstm_endpoint.py::test_unknown_heroes_handling", "tests/inference/test_lstm_endpoint.py::test_valid_prediction", "tests/model_lstm/test_lstm_metrics.py::test_model_accuracy_benchmark", "tests/model_lstm/test_lstm_metrics.py::test_model_architecture", "tests/model_lstm/test_lstm_metrics.py::test_model_consistency", "tests/model_lstm/test_lstm_metrics.py::test_model_exists", "tests/model_lstm/test_lstm_metrics.py::test_model_forward_pass", "tests/model_lstm/test_lstm_metrics.py::test_model_loads_correctly", "tests/model_lstm/test_lstm_metrics.py::test_model_predictions_are_valid", "tests/model_lstm/test_lstm_metrics.py::test_saved_metrics_exist", "tests/model_lstm/test_lstm_metrics.py::test_saved_metrics_format", "tests/model_v1/test_calibration.py::test_calibration_consistency", "tests/model_v1/test_calibration.py::test_model_calibration", "tests/model_v1/test_calibration.py::test_probability_distribution", "tests/model_v1/test_feature_importance_snapshot.py::test_feature_importance_distribution", "tests/model_v1/test_feature_importance_snapshot.py::test_feature_importance_snapshot", "tests/model_v1/test_feature_importance_snapshot.py::test_feature_importance_stability", "tests/model_v1/test_feature_importance_snapshot.py::test_hero_id_feature_coverage", "tests/model_v1/test_metrics.py::test_draft_model_v1_metrics", "tests/model_v1/test_metrics.py::test_feature_count_stability", "tests/model_v1/test_metrics.py::test_no_forbidden_features", "tests/model_v2/test_statistical_significance.py::test_baseline_comparison_with_real_model", "tests/model_v2/test_statistical_significance.py::test_improvement_evaluation", "tests/model_v2/test_statistical_significance.py::test_model_versioning", "tests/model_v2/test_statistical_significance.py::test_significance_thresholds", "tests/model_v2/test_statistical_significance.py::test_statistical_significance_framework", "tests/test_dataset_validation.py::test_match_schema[dataset/train_data/all_data_match_predict.csv]", "tests/test_db.py::TestDatabaseOperations::test_calculate_win_rate", "tests/test_db.py::TestDatabaseOperations::test_calculate_win_rate_database_error", "tests/test_db.py::TestDatabaseOperations::test_calculate_win_rate_no_predictions", "tests/test_db.py::TestDatabaseOperations::test_convert_to_native_type", "tests/test_db.py::TestDatabaseOperations::test_fetch_and_update_actual_results", "tests/test_db.py::TestDatabaseOperations::test_fetch_and_update_api_failure", "tests/test_db.py::TestDatabaseOperations::test_fetch_and_update_no_actual_result_in_response", "tests/test_db.py::TestDatabaseOperations::test_fetch_and_update_no_matches", "tests/test_db.py::TestDatabaseOperations::test_get_current_last_trained_row_id_exception_handling", "tests/test_db.py::TestDatabaseOperations::test_get_existing_last_trained_row_id", "tests/test_db.py::TestDatabaseOperations::test_get_history_data_as_dataframe", "tests/test_db.py::TestDatabaseOperations::test_get_no_last_trained_row_id", "tests/test_db.py::TestDatabaseOperations::test_insert_match_result", "tests/test_db.py::TestDatabaseOperations::test_update_actual_result", "tests/test_db.py::TestDatabaseOperations::test_update_existing_entry", "tests/test_db.py::TestDatabaseOperations::test_update_or_create_exception_handling", "tests/test_db.py::TestDatabaseOperations::test_update_or_create_new_entry", "tests/test_helpers.py::TestFeatureEngineering::test_calculate_player_kda", "tests/test_helpers.py::TestFeatureEngineering::test_calculate_team_features", "tests/test_helpers.py::TestFeatureEngineering::test_calculate_team_features_with_extreme_values", "tests/test_helpers.py::TestFeatureEngineering::test_find_dict_in_list", "tests/test_helpers.py::TestFeatureEngineering::test_prepare_data", "tests/test_helpers.py::TestFeatureEngineering::test_prepare_data_no_radiant_win", "tests/test_helpers.py::TestFeatureEngineering::test_prepare_hero_pick_data", "tests/test_helpers.py::TestFeatureEngineering::test_prepare_hero_pick_data_no_radiant_win", "tests/test_ml.py::TestMainML::test_evaluate_model", "tests/test_ml.py::TestMainML::test_incremental_training_with_enough_data", "tests/test_ml.py::TestMainML::test_incremental_training_with_no_data", "tests/test_ml.py::TestMainML::test_load_model", "tests/test_ml.py::TestMainML::test_predict", "tests/test_ml.py::TestMainML::test_train_and_save_model", "tests/test_struct.py::PlayerTest::test_get_player_total_data", "tests/test_struct.py::TestDota2API::test_get_live_tournaments_error", "tests/test_struct.py::TestDota2API::test_get_live_tournaments_no_matches", "tests/test_struct.py::TestDota2API::test_get_live_tournaments_success", "tests/test_struct.py::TestHero::test_get_hero_features_failure", "tests/test_struct.py::TestHero::test_get_hero_features_success", "tests/test_struct.py::TestHero::test_get_hero_matchups_failure", "tests/test_struct.py::TestHero::test_get_hero_matchups_success", "tests/test_struct.py::TestHero::test_repr", "tests/test_struct.py::TestHero::test_set_counter_pick_data", "tests/test_struct.py::TestHero::test_set_counter_pick_data_no_games_played", "tests/test_struct.py::TestMarkups::test_follow_dota_plus_for_selected_match", "tests/test_struct.py::TestMarkups::test_gen_dota2_matches_markup", "tests/test_struct.py::TestMarkups::test_gen_main_markup", "tests/test_struct.py::TestMarkups::test_gen_match_markup_by_id", "tests/test_struct.py::TestMarkups::test_make_prediction_for_selected_match", "tests/test_struct.py::TestMatch::test_get_match_data_api_failure", "tests/test_struct.py::TestMatch::test_get_match_data_for_prediction", "tests/test_struct.py::TestMatch::test_get_match_data_success", "tests/test_struct.py::TestMatch::test_repr", "tests/test_struct.py::TestMatch::test_set_hero_counter_picks", "tests/test_struct.py::TestTeam::test_add_multiple_players", "tests/test_struct.py::TestTeam::test_add_player", "tests/test_struct.py::TestTeam::test_repr_method", "tests/test_struct.py::TestTeam::test_team_initialization", "tests/test_struct.py::TestTournament::test_get_league_matches_failure", "tests/test_struct.py::TestTournament::test_get_league_matches_success"]