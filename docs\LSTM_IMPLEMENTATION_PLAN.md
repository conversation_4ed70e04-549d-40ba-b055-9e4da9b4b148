# LSTM-Based Draft-Phase Prediction Implementation Plan

This document outlines a focused, three-phase plan to implement the CBOW-LSTM model for pre-game draft prediction, integrating both the current improved PyTorch implementation and key methods from the original research.

## Phase 1: Generate Feature-Rich Sequential Draft Dataset
1. Use the updated script: `scripts/create_sequential_dataset_from_kaggle.py`.
2. Load and process data from Kaggle CSVs:
   - Source `picks_bans.csv` to get team-specific pick data (Radiant/Dire).
   - Source `main_metadata.csv` for match outcomes.
   - Source `Constants.Heroes.csv` for static hero features (roles, primary attribute, attack type).
   - Filter for picks, validate 5 picks per team, and create separate pick sequences for Ra<PERSON><PERSON> and <PERSON><PERSON>.
   - Aggregate hero features for each team's draft.
3. Save as `dataset/train_data/sequential_drafts_with_features.csv`.
   - **Columns**: `match_id`, `radiant_pick_sequence`, `dire_pick_sequence`, `radiant_win`, aggregated feature columns (e.g., `radiant_roles`), and `radiant_counter_rates`, `dire_counter_rates`.

## Phase 2: Compute Counter-Rates and Train Embeddings
1.  **Compute Counter-Rates**: Use the new `scripts/compute_hero_counter_rates.py` to generate a hero-vs-hero win probability matrix using the Log5 formula. This is integrated into the main data generation script.
2.  **Train Hero Embeddings (CBOW)**:
    - Combine `radiant_pick_sequence` and `dire_pick_sequence` to form a corpus of 5-hero "sentences".
    - Use Gensim’s Word2Vec (CBOW) to train embeddings:
1. Combine `radiant_pick_sequence` and `dire_pick_sequence` to form a corpus of 5-hero "sentences".
2. Use Gensim’s Word2Vec (CBOW) to train embeddings:
   - Vector size: 150  
   - Window: 5  
   - Min count: 1  
   - Workers: 8  
   - Epochs: 10  
3. Save the model to `models/hero_embeddings.word2vec` and `models/hero_embeddings.kv`.

## Phase 3: Build and Evaluate Dual-Input LSTM Model
1. Update training script: `ml/create_model_lstm.py`.
2. Data pipeline:
   - Load `dataset/train_data/sequential_drafts_with_features.csv`.
   - Map `radiant_pick_sequence` and `dire_pick_sequence` to their vector representations.
   - Incorporate the new aggregated hero features as additional model inputs.
   - Split into train/test sets.
3. Model architecture (PyTorch):
   - **Multi-Input Structure**: Create a multi-input model mirroring the paper's architecture:
     - Two LSTM streams for Radiant and Dire hero pick sequences.
     - Two input streams for the corresponding Log5 counter-rates.
     - Additional input streams for the aggregated static hero features.
   - The LSTM outputs will be multiplied by the counter-rates, then concatenated with the processed static features before the final classification layers.
   - Dense layers: Linear(128→64) → ReLU → Dropout(0.15) → Linear(64→32) → ReLU → Dropout(0.15).
   - Output layer: Linear(32→1) with Sigmoid activation for Radiant win probability.
4. Training configuration:
   - Loss: Binary Cross-Entropy  
   - Optimizer: Adam (lr=1e-4, weight_decay=1e-5)  
   - Callbacks: EarlyStopping (patience=30), ReduceLROnPlateau (patience=15, factor=0.5, min_lr=1e-6)  
   - Epochs: up to 150  
   - Batch size: 1024  
5. Save the final trained model to `models/lstm_model.pth`.
6. Evaluation:
   - Compute Accuracy, ROC-AUC, Log Loss on the test set.
   - Target benchmark: ≥73% accuracy (as reported in the paper).
7. Automated tests:
   - `tests/model_lstm/test_lstm_metrics.py`: load `models/lstm_model.pth` and assert accuracy ≥0.70 on a fixed test split.
   - `tests/inference/test_lstm_endpoint.py`: send sample pick sequences to the LSTM endpoint and assert valid probability outputs (0–1).

---

This plan reflects the current PyTorch implementation. Next steps are to implement the hero counter-rate feature using the log5 formula and integrate it into the model. Completing these enhancements will align the project with the original paper’s methodology and achieve the reported accuracy.
