# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Incremental improvements framework for draft-phase model.
Implements systematic feature additions and model versioning.
"""

import pandas as pd
import numpy as np
import logging
import json
import os
import sys
from datetime import datetime
from sklearn.model_selection import train_test_split, GroupKFold
from sklearn.metrics import accuracy_score, log_loss, roc_auc_score, brier_score_loss
import xgboost as xgb
from scipy import stats

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dataset.feature_schemas import (
    validate_features_for_draft_prediction,
    get_v2_enhanced_features,
    get_v3_advanced_features
)
from structure.helpers import prepare_draft_phase_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelVersionManager:
    """Manages model versions and incremental improvements."""
    
    def __init__(self, base_model_path="xgb_model_draft.pkl"):
        self.base_model_path = base_model_path
        self.metrics_dir = "tests/expected"
        self.models_dir = "models/versions"
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(self.metrics_dir, exist_ok=True)
    
    def load_baseline_metrics(self):
        """Load baseline metrics for comparison."""
        baseline_path = os.path.join(self.metrics_dir, "draft_v1_metrics.json")
        if os.path.exists(baseline_path):
            with open(baseline_path, 'r') as f:
                return json.load(f)
        else:
            # Default baseline if no metrics exist
            return {
                "accuracy": 0.52,
                "log_loss": 0.677,
                "roc_auc": 0.52,
                "brier_score": 0.25
            }
    
    def evaluate_model_improvement(self, current_metrics, baseline_metrics, 
                                 min_accuracy_gain=0.02, max_logloss_increase=0.02):
        """Evaluate if current model shows significant improvement."""
        
        improvements = {}
        
        # Check accuracy improvement
        acc_gain = current_metrics["accuracy"] - baseline_metrics["accuracy"]
        improvements["accuracy_gain"] = acc_gain
        improvements["accuracy_significant"] = acc_gain >= min_accuracy_gain
        
        # Check log loss improvement (lower is better)
        ll_change = current_metrics["log_loss"] - baseline_metrics["log_loss"]
        improvements["logloss_change"] = ll_change
        improvements["logloss_improved"] = ll_change <= max_logloss_increase
        
        # Check AUC improvement
        auc_gain = current_metrics["roc_auc"] - baseline_metrics["roc_auc"]
        improvements["auc_gain"] = auc_gain
        improvements["auc_significant"] = auc_gain >= 0.01
        
        # Overall improvement decision
        improvements["overall_improved"] = (
            improvements["accuracy_significant"] and 
            improvements["logloss_improved"]
        )
        
        return improvements
    
    def statistical_significance_test(self, y_true, y_pred_baseline, y_pred_new, 
                                    test_type="mcnemar"):
        """Test statistical significance of improvement."""
        
        if test_type == "mcnemar":
            # McNemar's test for paired predictions
            baseline_correct = (y_pred_baseline == y_true)
            new_correct = (y_pred_new == y_true)
            
            # Create contingency table
            both_correct = np.sum(baseline_correct & new_correct)
            baseline_only = np.sum(baseline_correct & ~new_correct)
            new_only = np.sum(~baseline_correct & new_correct)
            both_wrong = np.sum(~baseline_correct & ~new_correct)
            
            # McNemar's test statistic
            if baseline_only + new_only > 0:
                chi2 = (abs(baseline_only - new_only) - 1)**2 / (baseline_only + new_only)
                p_value = 1 - stats.chi2.cdf(chi2, 1)
            else:
                p_value = 1.0
            
            return {
                "test_type": "mcnemar",
                "chi2_statistic": chi2 if baseline_only + new_only > 0 else 0,
                "p_value": p_value,
                "significant": p_value < 0.05,
                "contingency_table": {
                    "both_correct": both_correct,
                    "baseline_only": baseline_only,
                    "new_only": new_only,
                    "both_wrong": both_wrong
                }
            }
        
        elif test_type == "bootstrap":
            # Bootstrap test for accuracy difference
            n_bootstrap = 1000
            n_samples = len(y_true)
            
            baseline_acc = accuracy_score(y_true, y_pred_baseline)
            new_acc = accuracy_score(y_true, y_pred_new)
            observed_diff = new_acc - baseline_acc
            
            # Bootstrap sampling
            bootstrap_diffs = []
            for _ in range(n_bootstrap):
                indices = np.random.choice(n_samples, n_samples, replace=True)
                boot_baseline_acc = accuracy_score(y_true[indices], y_pred_baseline[indices])
                boot_new_acc = accuracy_score(y_true[indices], y_pred_new[indices])
                bootstrap_diffs.append(boot_new_acc - boot_baseline_acc)
            
            # Calculate p-value (two-tailed)
            bootstrap_diffs = np.array(bootstrap_diffs)
            p_value = np.mean(np.abs(bootstrap_diffs) >= np.abs(observed_diff))
            
            return {
                "test_type": "bootstrap",
                "observed_difference": observed_diff,
                "bootstrap_mean": np.mean(bootstrap_diffs),
                "bootstrap_std": np.std(bootstrap_diffs),
                "p_value": p_value,
                "significant": p_value < 0.05,
                "confidence_interval": np.percentile(bootstrap_diffs, [2.5, 97.5])
            }
    
    def save_model_version(self, model, version_name, metrics, feature_list, 
                          improvement_analysis=None):
        """Save a new model version with metadata."""
        
        version_dir = os.path.join(self.models_dir, version_name)
        os.makedirs(version_dir, exist_ok=True)
        
        # Save model
        model_path = os.path.join(version_dir, f"{version_name}.pkl")
        model.save_model(model_path)
        
        # Save metadata
        metadata = {
            "version_name": version_name,
            "created_at": datetime.now().isoformat(),
            "metrics": metrics,
            "feature_count": len(feature_list),
            "features": feature_list,
            "improvement_analysis": improvement_analysis,
            "model_path": model_path
        }
        
        metadata_path = os.path.join(version_dir, "metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Update current metrics
        current_metrics_path = os.path.join(self.metrics_dir, f"{version_name}_metrics.json")
        with open(current_metrics_path, 'w') as f:
            json.dump(metrics, f, indent=2)
        
        logger.info(f"✅ Saved model version {version_name}")
        logger.info(f"   Model: {model_path}")
        logger.info(f"   Metadata: {metadata_path}")
        logger.info(f"   Features: {len(feature_list)}")
        
        return version_dir

def create_enhanced_dataset_v2():
    """Create enhanced dataset with player expertise and global win rates."""
    logger.info("Creating enhanced dataset v2 with player expertise features")
    
    # This would integrate with the existing generate_dataset_draft_phase.py
    # For now, return a placeholder indicating the framework is ready
    
    logger.info("📋 Enhanced dataset v2 framework ready")
    logger.info("   Next steps:")
    logger.info("   1. Run dataset/generate_dataset_draft_phase.py with API integration")
    logger.info("   2. Populate player expertise features from OpenDota API")
    logger.info("   3. Add global hero win rates (cached daily)")
    logger.info("   4. Generate team-level aggregations")
    
    return None

def implement_group_kfold_validation():
    """Implement GroupKFold validation by tournament/month."""
    logger.info("Setting up GroupKFold validation framework")
    
    # This would require tournament/date information in the dataset
    validation_config = {
        "method": "GroupKFold",
        "n_splits": 5,
        "group_by": "tournament_month",  # or "tournament_id"
        "stratify": True,
        "random_state": 42
    }
    
    logger.info("📋 GroupKFold validation framework ready")
    logger.info(f"   Configuration: {validation_config}")
    
    return validation_config

def create_hero_synergy_features():
    """Framework for hero synergy feature creation."""
    logger.info("Setting up hero synergy features framework")
    
    synergy_config = {
        "data_source": "OpenDota /heroes/combos API",
        "features": [
            "team_synergy_score",
            "best_combo_winrate", 
            "worst_combo_winrate",
            "role_balance_score"
        ],
        "cache_duration": "24h",
        "min_games_threshold": 100
    }
    
    logger.info("📋 Hero synergy framework ready")
    logger.info(f"   Configuration: {synergy_config}")
    
    return synergy_config

def create_neural_ensemble_framework():
    """Framework for neural network ensemble."""
    logger.info("Setting up neural network ensemble framework")
    
    ensemble_config = {
        "architecture": "Simple MLP",
        "layers": [64, 32, 16, 1],
        "activation": "ReLU",
        "dropout": 0.2,
        "embedding_dim": 16,  # For hero IDs
        "ensemble_method": "simple_average",  # or "weighted_average"
        "xgb_weight": 0.7,
        "nn_weight": 0.3
    }
    
    logger.info("📋 Neural ensemble framework ready")
    logger.info(f"   Configuration: {ensemble_config}")
    
    return ensemble_config

if __name__ == "__main__":
    # Demonstrate the framework
    logger.info("🚀 Incremental Improvements Framework")
    logger.info("=" * 50)
    
    # Initialize version manager
    version_manager = ModelVersionManager()
    baseline_metrics = version_manager.load_baseline_metrics()
    logger.info(f"Baseline metrics loaded: {baseline_metrics}")
    
    # Show available improvements
    logger.info("\n📈 Available Improvements:")
    logger.info("1. Enhanced dataset v2 (player expertise + global win rates)")
    logger.info("2. GroupKFold validation (tournament-based)")
    logger.info("3. Hero synergy features (R&D track)")
    logger.info("4. Neural network ensemble (R&D track)")
    
    # Initialize frameworks
    create_enhanced_dataset_v2()
    implement_group_kfold_validation()
    create_hero_synergy_features()
    create_neural_ensemble_framework()
    
    logger.info("\n✅ All improvement frameworks initialized and ready for implementation")
