"""
Unit tests for compute_team_performance.py

Tests the logic for calculating team performance statistics.
"""

import pandas as pd
import pytest
import numpy as np
import sys
from pathlib import Path

# Add scripts directory to path so we can import the module
sys.path.append(str(Path(__file__).parent.parent.parent / "scripts"))

from compute_team_performance import calculate_team_performance

class TestCalculateTeamPerformance:
    """Test the calculate_team_performance function with various scenarios."""
    
    def test_basic_team_performance_calculation(self):
        """Test basic global win rate and head-to-head calculation."""
        # Arrange: Create mock team match data
        mock_data = pd.DataFrame({
            'match_id': [1, 2, 3, 4, 5],
            'radiant_team_id': [100, 100, 200, 200, 100],
            'dire_team_id': [200, 300, 300, 100, 200],
            'radiant_win': [1, 1, 0, 1, 0]  # Team 100: 2 wins as radiant, 0 wins as dire = 2/4 = 0.5
        })

        # Act: Run the function
        result_df = calculate_team_performance(mock_data)

        # Assert: Check global win rates
        team_100_global = result_df[
            (result_df['team_id'] == 100) &
            (result_df['stat_type'] == 'global')
        ].iloc[0]

        # Team 100 played 4 games (3 as radiant: matches 1,2,5; 1 as dire: match 4), won 2 games
        assert team_100_global['win_rate'] == 0.5
        assert team_100_global['total_games'] == 4
        
        # Check head-to-head: Team 100 vs Team 200
        h2h_100_vs_200 = result_df[
            (result_df['team_id'] == 100) & 
            (result_df['opponent_team_id'] == 200) &
            (result_df['stat_type'] == 'h2h')
        ]
        
        if len(h2h_100_vs_200) > 0:
            # Team 100 played Team 200 twice as radiant: won 1, lost 1 = 0.5 win rate
            assert h2h_100_vs_200.iloc[0]['win_rate'] == 0.5
            assert h2h_100_vs_200.iloc[0]['total_games'] == 2
    
    def test_missing_team_ids_handling(self):
        """Test that missing team IDs are handled correctly."""
        # Arrange: Create data with NaN team IDs
        mock_data = pd.DataFrame({
            'match_id': [1, 2, 3],
            'radiant_team_id': [100, np.nan, 200],
            'dire_team_id': [np.nan, 200, 300],
            'radiant_win': [1, 0, 1]
        })
        
        # Act
        result_df = calculate_team_performance(mock_data)
        
        # Assert: Should only process matches with at least one known team
        # Match 1: Team 100 vs unknown (-1) - should be included
        # Match 2: Unknown vs Team 200 - should be included  
        # Match 3: Team 200 vs Team 300 - should be included
        
        # Check that we have global stats for teams 100, 200, 300
        global_stats = result_df[result_df['stat_type'] == 'global']
        team_ids = set(global_stats['team_id'].values)
        
        assert 100 in team_ids
        assert 200 in team_ids
        assert 300 in team_ids
        assert -1 not in team_ids  # Unknown team should not have global stats
    
    def test_single_team_scenarios(self):
        """Test edge cases with teams that play very few games."""
        # Arrange: Single game scenarios
        mock_data = pd.DataFrame({
            'match_id': [1, 2],
            'radiant_team_id': [500, 600],
            'dire_team_id': [600, 700],
            'radiant_win': [1, 0]
        })
        
        # Act
        result_df = calculate_team_performance(mock_data)
        
        # Assert
        # Team 500: 1 game, 1 win = 1.0 win rate
        team_500_global = result_df[
            (result_df['team_id'] == 500) & 
            (result_df['stat_type'] == 'global')
        ].iloc[0]
        assert team_500_global['win_rate'] == 1.0
        assert team_500_global['total_games'] == 1
        
        # Team 600: 2 games (1 as radiant loss, 1 as dire loss) = 0.0 win rate
        team_600_global = result_df[
            (result_df['team_id'] == 600) &
            (result_df['stat_type'] == 'global')
        ].iloc[0]
        assert team_600_global['win_rate'] == 0.0
        assert team_600_global['total_games'] == 2
    
    def test_head_to_head_calculation(self):
        """Test head-to-head win rate calculation."""
        # Arrange: Multiple games between same teams
        mock_data = pd.DataFrame({
            'match_id': [1, 2, 3, 4],
            'radiant_team_id': [800, 800, 900, 900],
            'dire_team_id': [900, 900, 800, 800],
            'radiant_win': [1, 0, 1, 0]
        })
        
        # Act
        result_df = calculate_team_performance(mock_data)
        
        # Assert head-to-head records
        h2h_800_vs_900 = result_df[
            (result_df['team_id'] == 800) & 
            (result_df['opponent_team_id'] == 900) &
            (result_df['stat_type'] == 'h2h')
        ]
        
        # Team 800 vs Team 900: Team 800 was radiant twice, won 1/2 = 0.5
        assert len(h2h_800_vs_900) == 1
        assert h2h_800_vs_900.iloc[0]['win_rate'] == 0.5
        assert h2h_800_vs_900.iloc[0]['total_games'] == 2
        
        h2h_900_vs_800 = result_df[
            (result_df['team_id'] == 900) & 
            (result_df['opponent_team_id'] == 800) &
            (result_df['stat_type'] == 'h2h')
        ]
        
        # Team 900 vs Team 800: Team 900 was radiant twice, won 1/2 = 0.5
        assert len(h2h_900_vs_800) == 1
        assert h2h_900_vs_800.iloc[0]['win_rate'] == 0.5
        assert h2h_900_vs_800.iloc[0]['total_games'] == 2
    
    def test_result_dataframe_structure(self):
        """Test that the result DataFrame has the correct structure."""
        # Arrange
        mock_data = pd.DataFrame({
            'match_id': [1, 2],
            'radiant_team_id': [1000, 1100],
            'dire_team_id': [1100, 1200],
            'radiant_win': [1, 0]
        })
        
        # Act
        result_df = calculate_team_performance(mock_data)
        
        # Assert structure
        expected_columns = ['team_id', 'opponent_team_id', 'win_rate', 'total_games', 'stat_type']
        assert list(result_df.columns) == expected_columns
        
        # Check that we have both global and h2h stats
        stat_types = set(result_df['stat_type'].values)
        assert 'global' in stat_types
        assert 'h2h' in stat_types
        
        # Check data types
        assert result_df['team_id'].dtype in [int, np.int64]
        assert result_df['opponent_team_id'].dtype in [int, np.int64]
        assert result_df['win_rate'].dtype == float
        assert result_df['total_games'].dtype in [int, np.int64]
