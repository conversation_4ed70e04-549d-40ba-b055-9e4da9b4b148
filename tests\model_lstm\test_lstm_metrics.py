# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test LSTM model metrics and performance validation.
Validates that the LSTM model meets minimum accuracy requirements.
"""

import pytest
import torch
import numpy as np
import pandas as pd
import logging
import os
import sys
from typing import Tuple
import pickle

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ml.create_model_lstm import CBOWLSTMModel, DraftSequenceDataset, load_data, load_embeddings
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@pytest.fixture
def lstm_model():
    """Load the trained LSTM model."""
    model_path = "models/lstm_model.pth"
    
    if not os.path.exists(model_path):
        pytest.skip(f"LSTM model not found at {model_path}. Please train the model first.")
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        model = CBOWLSTMModel(
            embedding_dim=150,
            lstm_hidden_size=128,
            dropout_rate=0.15
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        return model
    except Exception as e:
        pytest.fail(f"Failed to load LSTM model: {e}")

@pytest.fixture
def test_data():
    """Load test data for evaluation."""
    try:
        sequences, labels = load_data()
        embeddings = load_embeddings()
        
        # Use same split as training (fixed random state)
        _, X_test, _, y_test = train_test_split(
            sequences, labels, test_size=0.2, random_state=42
        )
        
        return X_test, y_test, embeddings
    except Exception as e:
        pytest.skip(f"Could not load test data: {e}")

def test_model_exists():
    """Test that the LSTM model file exists."""
    model_path = "models/lstm_model.pth"
    assert os.path.exists(model_path), f"LSTM model not found at {model_path}"

def test_model_loads_correctly(lstm_model):
    """Test that the LSTM model loads without errors."""
    assert lstm_model is not None
    assert isinstance(lstm_model, CBOWLSTMModel)
    
    # Check model parameters
    param_count = sum(p.numel() for p in lstm_model.parameters())
    assert param_count > 0, "Model should have parameters"
    logger.info(f"Model has {param_count} parameters")

def test_model_architecture(lstm_model):
    """Test that the model has the correct architecture."""
    # Check that model has required components
    assert hasattr(lstm_model, 'radiant_lstm'), "Model should have radiant_lstm"
    assert hasattr(lstm_model, 'dire_lstm'), "Model should have dire_lstm"
    assert hasattr(lstm_model, 'radiant_attention'), "Model should have radiant_attention"
    assert hasattr(lstm_model, 'dire_attention'), "Model should have dire_attention"
    assert hasattr(lstm_model, 'dense1'), "Model should have dense1 layer"
    assert hasattr(lstm_model, 'dense2'), "Model should have dense2 layer"
    assert hasattr(lstm_model, 'output'), "Model should have output layer"

def test_model_forward_pass(lstm_model):
    """Test that the model can perform a forward pass."""
    # Create dummy input data
    batch_size = 2
    seq_len = 10  # Full sequence length
    embedding_dim = 150

    hero_sequence = torch.randn(batch_size, seq_len, embedding_dim)
    position_sequence = torch.randint(0, 10, (batch_size, seq_len))

    # Forward pass
    with torch.no_grad():
        output = lstm_model(hero_sequence, position_sequence)

    # Check output shape and range
    assert output.shape == (batch_size, 1), f"Expected output shape ({batch_size}, 1), got {output.shape}"
    assert torch.all(output >= 0) and torch.all(output <= 1), "Output should be between 0 and 1 (sigmoid)"

def test_model_accuracy_benchmark(lstm_model, test_data):
    """Test that the model meets the minimum accuracy requirement (≥70%)."""
    X_test, y_test, embeddings = test_data
    
    if len(X_test) < 2:
        pytest.skip("Not enough test data for meaningful accuracy test")
    
    # Create test dataset and loader
    test_dataset = DraftSequenceDataset(X_test, y_test, embeddings)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Evaluate model
    all_predictions = []
    all_labels = []
    
    lstm_model.eval()
    with torch.no_grad():
        for hero_sequence, position_sequence, labels in test_loader:
            outputs = lstm_model(hero_sequence, position_sequence)
            predictions = (outputs > 0.5).float().cpu().numpy().flatten()
            labels_np = labels.cpu().numpy().flatten()

            all_predictions.extend(predictions)
            all_labels.extend(labels_np)
    
    # Calculate accuracy
    accuracy = accuracy_score(all_labels, all_predictions)
    logger.info(f"Model accuracy on test set: {accuracy:.4f}")
    
    # Assert minimum accuracy requirement
    assert accuracy >= 0.70, f"Model accuracy {accuracy:.4f} is below the minimum requirement of 70%"

def test_model_predictions_are_valid(lstm_model, test_data):
    """Test that model predictions are valid probabilities."""
    X_test, y_test, embeddings = test_data
    
    if len(X_test) == 0:
        pytest.skip("No test data available")
    
    # Test with first sample
    test_sequence = X_test[0]
    
    # Convert to embeddings
    embedded_sequence = []
    for hero_id in test_sequence:
        hero_str = str(hero_id)
        if hero_str in embeddings.key_to_index:
            embedded_sequence.append(embeddings[hero_str])
        else:
            embedded_sequence.append(np.zeros(embeddings.vector_size))
    
    embedded_sequence = np.array(embedded_sequence)
    
    # Convert to tensors with positional information
    hero_sequence = torch.FloatTensor(embedded_sequence).unsqueeze(0)  # (1, 10, 150)
    position_sequence = torch.LongTensor(list(range(10))).unsqueeze(0)  # (1, 10)

    # Make prediction
    with torch.no_grad():
        prediction = lstm_model(hero_sequence, position_sequence)
        probability = prediction.item()
    
    # Validate prediction
    assert 0 <= probability <= 1, f"Prediction {probability} should be between 0 and 1"
    assert isinstance(probability, float), "Prediction should be a float"

def test_saved_metrics_exist():
    """Test that training metrics were saved."""
    metrics_path = "models/lstm_metrics.pkl"
    assert os.path.exists(metrics_path), f"Training metrics not found at {metrics_path}"

def test_saved_metrics_format():
    """Test that saved metrics have the correct format."""
    metrics_path = "models/lstm_metrics.pkl"
    
    if not os.path.exists(metrics_path):
        pytest.skip("Metrics file not found")
    
    try:
        with open(metrics_path, "rb") as f:
            metrics = pickle.load(f)
        
        # Check required keys
        required_keys = ['accuracy', 'roc_auc', 'log_loss']
        for key in required_keys:
            assert key in metrics, f"Missing key '{key}' in saved metrics"
        
        # Check data types
        assert isinstance(metrics['accuracy'], (int, float)), "Accuracy should be numeric"
        assert isinstance(metrics['roc_auc'], (int, float)), "ROC-AUC should be numeric"
        
        logger.info(f"Saved metrics: Accuracy={metrics['accuracy']:.4f}, "
                   f"ROC-AUC={metrics['roc_auc']:.4f}, Log Loss={metrics['log_loss']}")
        
    except Exception as e:
        pytest.fail(f"Failed to load or validate metrics: {e}")

def test_model_consistency():
    """Test that the model produces consistent results for the same input."""
    model_path = "models/lstm_model.pth"
    
    if not os.path.exists(model_path):
        pytest.skip("Model file not found")
    
    # Load model
    checkpoint = torch.load(model_path, map_location='cpu')
    model = CBOWLSTMModel(embedding_dim=150, lstm_hidden_size=128, dropout_rate=0.15)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Create test input
    hero_sequence = torch.randn(1, 10, 150)
    position_sequence = torch.LongTensor(list(range(10))).unsqueeze(0)

    # Make multiple predictions
    predictions = []
    with torch.no_grad():
        for _ in range(5):
            output = model(hero_sequence, position_sequence)
            predictions.append(output.item())
    
    # Check consistency (should be identical in eval mode)
    for pred in predictions[1:]:
        assert abs(pred - predictions[0]) < 1e-6, "Model should produce consistent results in eval mode"

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
