# Kaggle Source Data Validation Report

This report analyzes the raw Kaggle CSV files to verify key assumptions about the data structure before it is processed for model training.

## Folder: `2023`
- **Total Matches Analyzed**: 28527
- **Matches with Exactly 10 Picks**: 28527 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 28527 matches

## Folder: `2024`
- **Total Matches Analyzed**: 28271
- **Matches with Exactly 10 Picks**: 28271 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 28271 matches

## Folder: `202501`
- **Total Matches Analyzed**: 2942
- **Matches with Exactly 10 Picks**: 2942 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 2942 matches

## Folder: `202502`
- **Total Matches Analyzed**: 2317
- **Matches with Exactly 10 Picks**: 2317 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 2317 matches

## Folder: `202503`
- **Total Matches Analyzed**: 2238
- **Matches with Exactly 10 Picks**: 2238 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 2238 matches

## Folder: `202504`
- **Total Matches Analyzed**: 2399
- **Matches with Exactly 10 Picks**: 2399 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 2399 matches

## Folder: `202505`
- **Total Matches Analyzed**: 2207
- **Matches with Exactly 10 Picks**: 2207 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 2207 matches

## Folder: `202506`
- **Total Matches Analyzed**: 2315
- **Matches with Exactly 10 Picks**: 2315 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 2315 matches

## Folder: `202507`
- **Total Matches Analyzed**: 958
- **Matches with Exactly 10 Picks**: 958 (100.00%)
- **Matches with Other Pick Counts**: 0
- **Team Pick Order Column ('team') Present**: ❌ No
- **Invalid Hero IDs Found**: 0
- **Matches Missing Win/Loss Info**: 0
- **Pick Count Distribution**:
  - `10` picks: 958 matches

## Overall Summary
- **Total Matches Across All Folders**: 72174
- **Total Matches with 10 Picks**: 72174 (100.00%)
- **Total Matches with Other Pick Counts**: 0
- **Overall Success Rate (10 picks)**: 100.00%

### Key Findings & Recommendations
- **Critical Finding**: The `team` column, which specifies whether Radiant or Dire made a pick, is **MISSING** from the source data. The model currently cannot distinguish which team made each pick. This confirms a major assumption is invalid.
  - **Recommendation**: The chronological sequence is still valuable, but the model's understanding of draft symmetry (e.g., first-pick advantage) is limited. This must be acknowledged in the model's evaluation.
