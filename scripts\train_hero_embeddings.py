# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Train CBOW (Word2Vec) embeddings for heroes based on pick sequences.
Creates 150-dimensional hero embeddings for LSTM model training.
"""

import pandas as pd
import logging
import os
import sys
from typing import List
from gensim.models import Word2Vec
import numpy as np

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_sequential_dataset() -> pd.DataFrame:
    """Load the sequential draft dataset with features."""
    try:
        df = pd.read_csv("dataset/train_data/sequential_drafts_with_features.csv")
        logger.info(f"Loaded sequential dataset with features with {len(df)} matches")
        return df
    except FileNotFoundError:
        logger.error("Sequential drafts dataset with features not found. Please run scripts/create_sequential_dataset_from_kaggle.py first")
        raise
    except Exception as e:
        logger.error(f"Error loading sequential dataset with features: {e}")
        raise

def prepare_sentences(df: pd.DataFrame) -> List[List[str]]:
    """
    Convert pick sequences (radiant and dire) to sentences for Word2Vec training.
    Each 5-hero pick sequence becomes a sentence of hero IDs.
    
    Args:
        df: DataFrame with radiant_pick_sequence and dire_pick_sequence columns
        
    Returns:
        List of sentences (each sentence is a list of hero ID strings)
    """
    sentences = []
    
    for _, row in df.iterrows():
        radiant_sequence = row['radiant_pick_sequence']
        dire_sequence = row['dire_pick_sequence']
        
        # Parse the sequences and convert to strings (Word2Vec expects strings)
        radiant_hero_ids = [str(int(x.strip())) for x in radiant_sequence.split(',')]
        dire_hero_ids = [str(int(x.strip())) for x in dire_sequence.split(',')]
        
        sentences.append(radiant_hero_ids)
        sentences.append(dire_hero_ids) # Add both teams' sequences as separate sentences
    
    logger.info(f"Prepared {len(sentences)} sentences for training (2 per match)")
    if sentences:
        logger.info(f"Sample Radiant sentence: {sentences[0]}")
        if len(sentences) > 1:
            logger.info(f"Sample Dire sentence: {sentences[1]}")
    else:
        logger.info("No sentences prepared.")
    
    return sentences

def train_cbow_embeddings(sentences: List[List[str]]) -> Word2Vec:
    """
    Train CBOW Word2Vec model on hero pick sequences.
    
    Args:
        sentences: List of sentences (hero ID sequences)
        
    Returns:
        Trained Word2Vec model
    """
    logger.info("Starting CBOW embeddings training")
    
    # Improved CBOW parameters based on feedback
    model = Word2Vec(
        sentences=sentences,
        vector_size=150,        # 150-dimensional embeddings
        window=7,               # Increased context window to 7
        min_count=1,            # Include all heroes (even rare ones)
        workers=8,              # Use 8 worker threads
        epochs=30,              # Increased epochs to 30
        sg=0,                   # Use CBOW (skip-gram=0)
        hs=0,                   # Use negative sampling instead of hierarchical softmax
        negative=5,             # Negative sampling parameter
        alpha=0.025,            # Initial learning rate
        min_alpha=0.0001,       # Final learning rate
        seed=42                 # For reproducibility
    )
    
    logger.info("CBOW model training completed")
    logger.info(f"Vocabulary size: {len(model.wv.key_to_index)}")
    logger.info(f"Vector size: {model.wv.vector_size}")
    
    return model

def validate_embeddings(model: Word2Vec) -> bool:
    """
    Validate the trained embeddings.
    
    Args:
        model: Trained Word2Vec model
        
    Returns:
        True if validation passes
    """
    logger.info("Validating hero embeddings")
    
    # Check if we have embeddings for common heroes
    common_heroes = ['1', '2', '3', '4', '5']  # Some common hero IDs
    missing_heroes = []
    
    for hero_id in common_heroes:
        if hero_id not in model.wv.key_to_index:
            missing_heroes.append(hero_id)
    
    if missing_heroes:
        logger.warning(f"Missing embeddings for heroes: {missing_heroes}")
    
    # Check vector dimensions
    if model.wv.vector_size != 150:
        logger.error(f"Expected vector size 150, got {model.wv.vector_size}")
        return False
    
    # Test similarity functionality
    try:
        vocab_list = list(model.wv.key_to_index.keys())
        if len(vocab_list) >= 2:
            hero1, hero2 = vocab_list[0], vocab_list[1]
            similarity = model.wv.similarity(hero1, hero2)
            logger.info(f"Sample similarity between hero {hero1} and {hero2}: {similarity:.4f}")
        
        # Test vector retrieval
        if vocab_list:
            test_hero = vocab_list[0]
            vector = model.wv[test_hero]
            logger.info(f"Sample vector for hero {test_hero}: shape={vector.shape}, mean={vector.mean():.4f}")
            
    except Exception as e:
        logger.error(f"Error testing embeddings functionality: {e}")
        return False
    
    logger.info("✅ Embeddings validation passed")
    return True

def save_embeddings(model: Word2Vec, output_path: str = "models/hero_embeddings.word2vec"):
    """
    Save the trained embeddings model.
    
    Args:
        model: Trained Word2Vec model
        output_path: Path to save the model
    """
    # Ensure models directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save the model
    model.save(output_path)
    logger.info(f"Hero embeddings saved to {output_path}")
    
    # Also save just the key vectors for easier loading
    keyed_vectors_path = output_path.replace('.word2vec', '.kv')
    model.wv.save(keyed_vectors_path)
    logger.info(f"KeyedVectors saved to {keyed_vectors_path}")

def analyze_embeddings(model: Word2Vec):
    """Analyze the trained embeddings and show some insights."""
    logger.info("Analyzing hero embeddings")
    
    vocab_size = len(model.wv.key_to_index)
    logger.info(f"Total heroes in vocabulary: {vocab_size}")
    
    # Show most frequent heroes (by training frequency)
    hero_counts = {}
    for hero_id in model.wv.key_to_index:
        hero_counts[hero_id] = model.wv.get_vecattr(hero_id, "count")
    
    top_heroes = sorted(hero_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    logger.info("Top 10 most frequent heroes in training:")
    for hero_id, count in top_heroes:
        logger.info(f"  Hero {hero_id}: {count} occurrences")
    
    # Test some hero similarities if we have enough heroes
    if vocab_size >= 5:
        test_heroes = list(model.wv.key_to_index.keys())[:5]
        logger.info("Sample hero similarities:")
        for i in range(min(3, len(test_heroes))):
            for j in range(i+1, min(3, len(test_heroes))):
                sim = model.wv.similarity(test_heroes[i], test_heroes[j])
                logger.info(f"  Hero {test_heroes[i]} vs Hero {test_heroes[j]}: {sim:.4f}")

def train_hero_embeddings():
    """Main function to train hero embeddings."""
    logger.info("Starting hero embeddings training pipeline")
    
    # Load sequential dataset
    df = load_sequential_dataset()
    
    # Prepare sentences for Word2Vec
    sentences = prepare_sentences(df)
    
    if not sentences:
        logger.error("No sentences prepared for training")
        return False
    
    # Train CBOW embeddings
    model = train_cbow_embeddings(sentences)
    
    # Validate embeddings
    if not validate_embeddings(model):
        logger.error("Embeddings validation failed")
        return False
    
    # Save embeddings
    save_embeddings(model)
    
    # Analyze embeddings
    analyze_embeddings(model)
    
    logger.info("🎉 Hero embeddings training completed successfully!")
    return True

if __name__ == "__main__":
    try:
        success = train_hero_embeddings()
        if not success:
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        sys.exit(1)
