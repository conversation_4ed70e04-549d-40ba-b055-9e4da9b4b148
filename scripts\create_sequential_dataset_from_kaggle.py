import pandas as pd
import numpy as np
import os
from pathlib import Path
import logging
from collections import Counter
import pandera as pa
from pandera import Column, Check

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define paths
KAGGLE_DATA_DIR = Path("dataset/kaggle_data")
CONSTANTS_PATH = KAGGLE_DATA_DIR / "Constants" / "Constants.Heroes.csv"
COUNTER_RATES_PATH = Path('models') / 'hero_counter_rates.csv'
PLAYER_HERO_PERFORMANCE_PATH = Path('models') / 'player_hero_performance.csv'
TEAM_PERFORMANCE_PATH = Path('models') / 'team_performance.csv'
OUTPUT_DIR = Path('dataset/train_data')
OUTPUT_PATH = OUTPUT_DIR / 'sequential_drafts_with_features.csv'

# Specify which months to include
MONTHS = ["202501", "202502", "202503", "202504", "202505", "202506", "202507"]

def load_data():
    """Loads all monthly picks/bans, metadata, and players data from Kaggle CSVs."""
    picks_bans_dfs = []
    meta_dfs = []
    players_dfs = []

    for month in MONTHS:
        month_dir = KAGGLE_DATA_DIR / month
        picks_bans_file = month_dir / "picks_bans.csv"
        meta_file = month_dir / "main_metadata.csv"
        players_file = month_dir / "players.csv"

        if picks_bans_file.exists() and meta_file.exists() and players_file.exists():
            logging.info(f"Loading data for month: {month}")
            try:
                picks_bans_dfs.append(pd.read_csv(picks_bans_file))
                meta_dfs.append(pd.read_csv(meta_file, usecols=["match_id", "radiant_win"]))
                # Only load the columns we need from players.csv to save memory
                players_dfs.append(pd.read_csv(players_file, usecols=["match_id", "account_id", "hero_id", "team_number"]))
            except Exception as e:
                logging.error(f"Could not read CSVs in {month}: {e}")
        else:
            logging.warning(f"Warning: missing data for month {month}")

    if not picks_bans_dfs or not meta_dfs or not players_dfs:
        return None, None, None

    picks_bans_df = pd.concat(picks_bans_dfs, ignore_index=True)
    meta_df = pd.concat(meta_dfs, ignore_index=True).drop_duplicates(subset=["match_id"])
    players_df = pd.concat(players_dfs, ignore_index=True)
    return picks_bans_df, meta_df, players_df

def load_hero_features():
    """Loads hero features from Constants.Heroes.csv."""
    if not CONSTANTS_PATH.exists():
        logging.error(f"Hero constants file not found at: {CONSTANTS_PATH}")
        return None
    
    heroes_df = pd.read_csv(CONSTANTS_PATH, usecols=['id', 'roles', 'primary_attr', 'attack_type'])
    heroes_df = heroes_df.rename(columns={'id': 'hero_id'})
    
    # Safely evaluate roles string
    def parse_roles(roles_str):
        try:
            return eval(roles_str)
        except:
            return []
    heroes_df['roles'] = heroes_df['roles'].apply(parse_roles)
    
    return heroes_df

def load_player_hero_performance():
    """Loads player-hero performance data for lookup."""
    if not PLAYER_HERO_PERFORMANCE_PATH.exists():
        logging.error(f"Player-hero performance file not found at: {PLAYER_HERO_PERFORMANCE_PATH}")
        return None

    performance_df = pd.read_csv(PLAYER_HERO_PERFORMANCE_PATH, index_col=[0, 1])  # MultiIndex on account_id, hero_id
    logging.info(f"Loaded {len(performance_df)} player-hero performance records")
    return performance_df

def load_team_performance():
    """Loads team performance data for lookup."""
    if not TEAM_PERFORMANCE_PATH.exists():
        logging.error(f"Team performance file not found at: {TEAM_PERFORMANCE_PATH}")
        return None

    team_perf_df = pd.read_csv(TEAM_PERFORMANCE_PATH)
    logging.info(f"Loaded {len(team_perf_df)} team performance records")
    return team_perf_df

def aggregate_hero_features(pick_sequence, hero_features_map):
    """Aggregates features for a sequence of hero IDs."""
    roles = Counter()
    primary_attrs = Counter()
    attack_types = Counter()
    
    for hero_id in pick_sequence:
        features = hero_features_map.get(hero_id)
        if features:
            roles.update(features['roles'])
            primary_attrs.update([features['primary_attr']])
            attack_types.update([features['attack_type']])
            
    return {
        "roles": dict(roles),
        "primary_attrs": dict(primary_attrs),
        "attack_types": dict(attack_types)
    }

def get_player_hero_features(match_id, team_number, players_df, performance_df):
    """
    Get aggregated player-hero performance features for a team in a match.

    Args:
        match_id: The match ID
        team_number: 0 for Radiant, 1 for Dire
        players_df: DataFrame with player data
        performance_df: DataFrame with player-hero performance data (MultiIndex)

    Returns:
        dict with avg_win_rate and sum_total_games
    """
    # Get players for this match and team
    team_players = players_df[
        (players_df['match_id'] == match_id) &
        (players_df['team_number'] == team_number)
    ]

    if len(team_players) != 5:
        # If we don't have exactly 5 players, return default values
        return {"avg_win_rate": 0.5, "sum_total_games": 0}

    win_rates = []
    total_games = []

    for _, player in team_players.iterrows():
        account_id = player['account_id']
        hero_id = player['hero_id']

        # Handle missing account_id (anonymous players)
        if pd.isna(account_id):
            account_id = -1
        else:
            account_id = int(account_id)

        # Look up performance data
        try:
            if (account_id, hero_id) in performance_df.index:
                perf = performance_df.loc[(account_id, hero_id)]
                win_rates.append(perf['win_rate'])
                total_games.append(perf['total_games'])
            else:
                # No historical data for this player-hero combo, use defaults
                win_rates.append(0.5)  # Neutral win rate
                total_games.append(0)  # No games
        except Exception:
            # Fallback for any lookup errors
            win_rates.append(0.5)
            total_games.append(0)

    return {
        "avg_win_rate": np.mean(win_rates) if win_rates else 0.5,
        "sum_total_games": sum(total_games)
    }

def main():
    """Main function to generate the sequential draft dataset with features."""
    logging.info("Starting dataset generation...")

    picks_bans, meta, players = load_data()
    if picks_bans is None:
        logging.error("No picks/bans data loaded. Aborting.")
        return

    hero_features = load_hero_features()
    if hero_features is None:
        logging.error("No hero features loaded. Aborting.")
        return

    player_hero_performance = load_player_hero_performance()
    if player_hero_performance is None:
        logging.error("No player-hero performance data loaded. Aborting.")
        return

    hero_features_map = hero_features.set_index('hero_id').to_dict('index')

    # 1. Filter for picks only
    picks = picks_bans[picks_bans['is_pick']].copy()

    # 2. Group by match and team to create pick sequences
    sequences = (
        picks.sort_values(['match_id', 'order'])
             .groupby(['match_id', 'team'])['hero_id']
             .apply(list)
             .unstack(level='team')
    )
    sequences.columns = ['radiant_picks', 'dire_picks']
    sequences = sequences.reset_index()

    # 3. Validate 5 picks per team and format sequences
    sequences = sequences.dropna(subset=['radiant_picks', 'dire_picks'])
    sequences = sequences[
        (sequences['radiant_picks'].apply(len) == 5) & 
        (sequences['dire_picks'].apply(len) == 5)
    ]
    
    sequences['radiant_pick_sequence'] = sequences['radiant_picks'].apply(lambda ids: ','.join(map(str, ids)))
    sequences['dire_pick_sequence'] = sequences['dire_picks'].apply(lambda ids: ','.join(map(str, ids)))

    # 4. Merge with match outcome
    result = pd.merge(
        sequences[['match_id', 'radiant_pick_sequence', 'dire_pick_sequence', 'radiant_picks', 'dire_picks']],
        meta,
        on='match_id',
        how='inner'
    )

    # 5. Aggregate and add hero features
    logging.info("Aggregating hero features for each team...")
    radiant_features = result['radiant_picks'].apply(lambda x: aggregate_hero_features(x, hero_features_map))
    dire_features = result['dire_picks'].apply(lambda x: aggregate_hero_features(x, hero_features_map))

    result['radiant_roles'] = radiant_features.apply(lambda x: x['roles'])
    result['radiant_primary_attrs'] = radiant_features.apply(lambda x: x['primary_attrs'])
    result['radiant_attack_types'] = radiant_features.apply(lambda x: x['attack_types'])
    
    result['dire_roles'] = dire_features.apply(lambda x: x['roles'])
    result['dire_primary_attrs'] = dire_features.apply(lambda x: x['primary_attrs'])
    result['dire_attack_types'] = dire_features.apply(lambda x: x['attack_types'])

    # 6. Add player-hero performance features
    logging.info("Calculating player-hero performance features for each team...")

    radiant_player_features = []
    dire_player_features = []

    for _, row in result.iterrows():
        match_id = row['match_id']

        # Get Radiant (team 0) player-hero features
        radiant_features = get_player_hero_features(match_id, 0, players, player_hero_performance)
        radiant_player_features.append(radiant_features)

        # Get Dire (team 1) player-hero features
        dire_features = get_player_hero_features(match_id, 1, players, player_hero_performance)
        dire_player_features.append(dire_features)

    # Add the new columns
    result['radiant_avg_player_hero_win_rate'] = [f['avg_win_rate'] for f in radiant_player_features]
    result['radiant_sum_player_hero_games'] = [f['sum_total_games'] for f in radiant_player_features]
    result['dire_avg_player_hero_win_rate'] = [f['avg_win_rate'] for f in dire_player_features]
    result['dire_sum_player_hero_games'] = [f['sum_total_games'] for f in dire_player_features]

    # 7. Add counter-rate features
    logging.info("Loading and applying hero counter-rates...")
    if not COUNTER_RATES_PATH.exists():
        logging.error(f"Counter rates file not found at {COUNTER_RATES_PATH}. Skipping this feature.")
    else:
        counter_rates_df = pd.read_csv(COUNTER_RATES_PATH, index_col=0)
        counter_rates_df.columns = counter_rates_df.columns.astype(int) # Ensure columns are int for lookup

        def get_avg_counter_rates(team_picks, opponent_picks):
            avg_rates = []
            for hero_id in team_picks:
                rates = [counter_rates_df.loc[hero_id, opp_id] for opp_id in opponent_picks if opp_id in counter_rates_df.columns and hero_id in counter_rates_df.index]
                avg_rates.append(np.mean(rates) if rates else 0.5)
            return avg_rates

        result['radiant_counter_rates'] = result.apply(lambda row: get_avg_counter_rates(row['radiant_picks'], row['dire_picks']), axis=1)
        result['dire_counter_rates'] = result.apply(lambda row: get_avg_counter_rates(row['dire_picks'], row['radiant_picks']), axis=1)

    # 8. Clean up and save
    # Convert data types for consistency
    result['radiant_win'] = result['radiant_win'].astype('int32')
    result['radiant_sum_player_hero_games'] = result['radiant_sum_player_hero_games'].astype('int64')
    result['dire_sum_player_hero_games'] = result['dire_sum_player_hero_games'].astype('int64')

    final_cols = [
        'match_id',
        'radiant_pick_sequence',
        'dire_pick_sequence',
        'radiant_win',
        'radiant_roles',
        'radiant_primary_attrs',
        'radiant_attack_types',
        'dire_roles',
        'dire_primary_attrs',
        'dire_attack_types',
        'radiant_avg_player_hero_win_rate',
        'radiant_sum_player_hero_games',
        'dire_avg_player_hero_win_rate',
        'dire_sum_player_hero_games',
        'radiant_counter_rates',
        'dire_counter_rates'
    ]
    # Filter to only include columns that actually exist, in case counter-rates failed
    result = result[[col for col in final_cols if col in result.columns]]

    # 9. Data validation using pandera
    logging.info("Validating final DataFrame schema...")

    # Define the data contract
    schema = pa.DataFrameSchema({
        "match_id": Column("int64"),
        "radiant_win": Column("int32", Check.isin([0, 1])),  # Allow int32 for radiant_win
        "radiant_pick_sequence": Column(object),  # String of comma-separated hero IDs
        "dire_pick_sequence": Column(object),     # String of comma-separated hero IDs

        # --- New Tier 1.1 Features ---
        "radiant_avg_player_hero_win_rate": Column(float, Check.between(0.0, 1.0), nullable=False),
        "radiant_sum_player_hero_games": Column("int64", Check.greater_than_or_equal_to(0)),
        "dire_avg_player_hero_win_rate": Column(float, Check.between(0.0, 1.0), nullable=False),
        "dire_sum_player_hero_games": Column("int64", Check.greater_than_or_equal_to(0)),
    })

    try:
        # Validate the DataFrame. This will raise a detailed error if it fails.
        validated_df = schema.validate(result)
        logging.info("Validation successful! DataFrame adheres to the contract.")

        # Save the file
        OUTPUT_DIR.mkdir(exist_ok=True)
        validated_df.to_csv(OUTPUT_PATH, index=False)

        logging.info(f"Successfully generated dataset with {len(validated_df)} matches.")
        logging.info(f"Dataset saved to {OUTPUT_PATH}")

    except pa.errors.SchemaError as err:
        logging.error("SCHEMA VALIDATION ERROR!")
        # err.failure_cases is a dataframe that shows you exactly what rows failed and why
        logging.error(f"Failure cases:\n{err.failure_cases}")
        # Save the failed data for debugging
        failed_data_path = OUTPUT_DIR / 'failed_validation_data.csv'
        err.data.to_csv(failed_data_path, index=False)
        logging.error(f"Failed validation data saved to {failed_data_path}")
        raise err  # Stop the process

if __name__ == '__main__':
    main()
