import os
import csv

def summarize_csv_files(directories, output_file, lines_to_read=20):
    """
    Extracts headers and a specified number of lines from CSV files
    in a list of directories and writes them to a markdown file.
    """
    with open(output_file, 'w', encoding='utf-8') as md_file:
        md_file.write("# Kaggle CSV Data Summary\n\n")

        for directory in directories:
            md_file.write(f"## Directory: `{directory}`\n\n")
            for filename in sorted(os.listdir(directory)):
                if filename.endswith('.csv'):
                    file_path = os.path.join(directory, filename)
                    md_file.write(f"### File: `{file_path}`\n\n")
                    md_file.write("```csv\n")
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as csv_file:
                            reader = csv.reader(csv_file)
                            # Write header
                            header = next(reader, None)
                            if header:
                                md_file.write(','.join(header) + '\n')
                                # Write specified number of lines
                                for i, row in enumerate(reader):
                                    if i >= lines_to_read:
                                        break
                                    md_file.write(','.join(row) + '\n')
                            else:
                                md_file.write("File is empty or has no header.\n")
                    except Exception as e:
                        md_file.write(f"Error reading file: {e}\n")
                    md_file.write("```\n\n")

if __name__ == '__main__':
    kaggle_dirs = [
        'dataset/kaggle_data/202501',
        'dataset/kaggle_data/Constants'
    ]
    output_md_file = 'reports/kaggle_csv_summary.md'
    summarize_csv_files(kaggle_dirs, output_md_file)
    print(f"Summary written to {output_md_file}")
