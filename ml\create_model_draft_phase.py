# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Train draft-phase only model for tournament match prediction.
Uses only pre-game information available at draft time to prevent data leakage.
"""

import pandas as pd
import numpy as np
import logging
import os
import sys
from sklearn.model_selection import train_test_split, GroupKFold
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml.model import MainML
from structure.helpers import prepare_draft_phase_data
from dataset.feature_schemas import validate_features_for_draft_prediction

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_and_validate_data():
    """Load and validate draft-phase dataset."""
    logger.info("Loading draft-phase dataset")
    
    try:
        # Try loading the new draft-phase dataset
        df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
        logger.info(f"Loaded draft-phase dataset with {len(df)} matches")
    except FileNotFoundError:
        logger.error("Draft-phase dataset not found. Please run dataset/generate_dataset_draft_phase.py first")
        raise
    
    # Validate no forbidden features are present
    is_valid, forbidden = validate_features_for_draft_prediction(df.columns.tolist())
    if not is_valid:
        logger.error(f"❌ Dataset contains forbidden post-match features: {forbidden}")
        raise ValueError(f"Data leakage detected: {forbidden}")
    
    logger.info("✅ Dataset validation passed - no data leakage detected")
    return df

def train_draft_phase_model():
    """Train XGBoost model using only draft-phase features."""
    
    # Load and validate data
    df = load_and_validate_data()
    
    # Prepare data
    logger.info("Preparing draft-phase data for training")
    df = prepare_draft_phase_data(df, "scaler_draft.pkl")
    
    # Split features and target
    X = df.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    y = df['radiant_win']
    
    logger.info(f"Training features: {X.columns.tolist()}")
    logger.info(f"Feature matrix shape: {X.shape}")
    logger.info(f"Target distribution: {y.value_counts().to_dict()}")
    
    # Use GroupKFold for proper validation (group by match_id if available)
    # For now, use regular train_test_split with temporal awareness
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    logger.info(f"Training set: {X_train.shape}, Test set: {X_test.shape}")
    
    # XGBoost parameters optimized for draft-phase features
    params = {
        'objective': 'binary:logistic',
        'eval_metric': 'logloss',
        'device': 'cuda',
        'tree_method': 'hist',
        'max_depth': 4,  # Reduced depth for draft-phase features
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,  # L1 regularization
        'reg_lambda': 1.0,  # L2 regularization
        'min_child_weight': 3,
        'gamma': 0.1,
        'random_state': 42,
        'verbosity': 1
    }
    
    # Create DMatrix for XGBoost
    dtrain = xgb.DMatrix(X_train, label=y_train)
    dtest = xgb.DMatrix(X_test, label=y_test)
    
    # Train model with early stopping
    logger.info("Training XGBoost model...")
    model = xgb.train(
        params,
        dtrain,
        num_boost_round=1000,
        evals=[(dtrain, 'train'), (dtest, 'eval')],
        early_stopping_rounds=50,
        verbose_eval=100
    )
    
    logger.info("✅ Model training completed")
    
    # Save model
    model_path = "xgb_model_draft.pkl"
    model.save_model(model_path)
    logger.info(f"Model saved to {model_path}")
    
    # Evaluate model
    logger.info("Evaluating model performance...")
    y_pred = model.predict(dtest)
    y_pred_binary = (y_pred > 0.5).astype(int)
    
    # Print evaluation metrics
    logger.info("\n" + "="*50)
    logger.info("DRAFT-PHASE MODEL EVALUATION")
    logger.info("="*50)
    
    report = classification_report(y_test, y_pred_binary)
    logger.info(f"Classification Report:\n{report}")
    
    cm = confusion_matrix(y_test, y_pred_binary)
    logger.info(f"Confusion Matrix:\n{cm}")
    
    # Calculate accuracy
    accuracy = (y_pred_binary == y_test).mean()
    logger.info(f"Accuracy: {accuracy:.4f}")
    
    # Feature importance
    importance = model.get_score(importance_type='weight')
    logger.info("\nTop 10 Most Important Features:")
    for i, (feature, score) in enumerate(sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]):
        logger.info(f"{i+1}. {feature}: {score}")
    
    # Test prediction on sample data
    logger.info("\nTesting prediction on sample data...")
    sample_data = X_test.iloc[:5]
    sample_pred = model.predict(xgb.DMatrix(sample_data))
    sample_actual = y_test.iloc[:5]

    for i, (pred, actual) in enumerate(zip(sample_pred, sample_actual)):
        logger.info(f"Sample {i+1}: Predicted={pred:.4f}, Actual={actual}")
    
    # Final validation
    if accuracy < 0.95:  # Reasonable accuracy for draft-phase prediction
        logger.info("✅ Model shows realistic performance - no data leakage detected")
    else:
        logger.warning("⚠️ Accuracy is suspiciously high - please verify no data leakage")
    
    return model

if __name__ == "__main__":
    try:
        model = train_draft_phase_model()
        logger.info("🎉 Draft-phase model training completed successfully!")
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise
