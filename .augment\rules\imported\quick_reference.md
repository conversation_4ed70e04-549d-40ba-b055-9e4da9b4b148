---
type: "manual"
description: "Example description"
---
# Dota 2 Predictor - Quick Reference for AI Agent

## 🚀 Essential Commands

### Environment Setup
```bash
mamba activate dota2predictor-env    # Activate environment
python verify_gpu.py                # Test GPU functionality
jupyter lab                         # Start development environment
python start.py                     # Run the bot
```

### Development Workflow
```bash
python -m pytest tests/             # Run all tests
python -m pytest tests/test_ml.py   # Run specific test file
coverage run -m pytest              # Run tests with coverage
pre-commit run --all-files          # Run code quality checks
```

### Model Operations
```bash
python ml/create_model_match_predict.py    # Train match prediction model
python ml/create_model_hero_pick.py        # Train hero pick model
python ml/create_model_dota_plus.py        # Train Dota Plus model
```

## 📁 Key File Locations

### Model Files (Project Root)
- `xgb_model.pkl` - Main match prediction model
- `xgb_model_hero_pick.pkl` - Hero pick analysis model  
- `xgb_model_dota_plus.pkl` - Dota Plus live tracking model
- `scaler.pkl` - Feature scaler for main model
- `scaler_dota_plus.pkl` - Feature scaler for Dota Plus model

### Core Modules
- `ml/model.py` - Main ML class (`MainML`)
- `db/database_operations.py` - Database functions
- `structure/helpers.py` - Data preparation utilities
- `structure/opendota.py` - OpenDota API integration
- `start.py` - Bot entry point
- `config.py` - Configuration management

### Data and Testing
- `dataset/` - Training data and schemas
- `tests/` - Unit tests for all components
- `docs/` - Documentation and setup guides

## 🔧 Common Code Patterns

### Load Model Safely
```python
import xgboost as xgb
from pathlib import Path

def load_model(model_path: str) -> xgb.Booster:
    if not Path(model_path).exists():
        raise FileNotFoundError(f"Model not found: {model_path}")
    
    model = xgb.Booster()
    model.load_model(model_path)
    return model
```

### Database Query with Session
```python
from db.database_operations import get_db_session

def get_match_data(match_id: int):
    with get_db_session() as session:
        result = session.query(Match).filter_by(id=match_id).first()
        return result.to_dict() if result else None
```

### Bot Command Handler
```python
@bot.message_handler(commands=['predict'])
def handle_predict(message):
    try:
        # Process command
        result = process_prediction(message.text)
        bot.reply_to(message, f"✅ Prediction: {result}")
    except Exception as e:
        logger.error(f"Command failed: {e}")
        bot.reply_to(message, "❌ Error occurred")
```

### XGBoost GPU Training
```python
params = {
    'objective': 'binary:logistic',
    'device': 'cuda',
    'tree_method': 'hist',
    'max_depth': 6,
    'learning_rate': 0.1
}

model = xgb.train(params, dtrain, num_boost_round=1000)
```

## 🎯 Key Classes and Functions

### MainML Class (ml/model.py)
```python
class MainML:
    def __init__(self):
        self.model = None
        self.scaler = None
    
    def load_model(self, model_path: str):
        """Load XGBoost model"""
    
    def predict(self, features: np.ndarray) -> float:
        """Make prediction"""
    
    def train_model(self, X_train, y_train):
        """Train new model"""
```

### Database Operations (db/database_operations.py)
```python
def get_history_data_as_dataframe() -> pd.DataFrame:
    """Get historical match data for training"""

def save_prediction(user_id: int, prediction: dict):
    """Save prediction to database"""

def get_user_stats(user_id: int) -> dict:
    """Get user prediction statistics"""
```

### Helper Functions (structure/helpers.py)
```python
def prepare_match_prediction_data(match_data: dict) -> np.ndarray:
    """Prepare match data for prediction"""

def validate_hero_ids(hero_ids: List[int]) -> bool:
    """Validate hero ID list"""

def calculate_team_synergy(heroes: List[int]) -> float:
    """Calculate team synergy score"""
```

## 🐛 Debugging Checklist

### Model Issues
- [ ] Check if model file exists
- [ ] Verify GPU availability (`nvidia-smi`)
- [ ] Check CUDA toolkit installation (`mamba list cuda`)
- [ ] Validate input feature dimensions
- [ ] Check model loading logs

### Database Issues
- [ ] Verify database connection
- [ ] Check table schema
- [ ] Validate query parameters
- [ ] Review connection pool status
- [ ] Check for locked transactions

### Bot Issues
- [ ] Verify bot token configuration
- [ ] Check command parsing logic
- [ ] Review error handling
- [ ] Validate user input format
- [ ] Check API rate limits

### API Issues
- [ ] Check OpenDota API status
- [ ] Verify rate limiting implementation
- [ ] Review request/response logging
- [ ] Check network connectivity
- [ ] Validate API response format

## 📊 Performance Monitoring

### GPU Monitoring
```bash
nvidia-smi                          # Check GPU usage
watch -n 1 nvidia-smi               # Monitor GPU continuously
```

### Database Monitoring
```sql
-- Check slow queries
SELECT query, mean_time, calls FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- Check connection count
SELECT count(*) FROM pg_stat_activity;
```

### Application Monitoring
```python
import psutil
import time

# Monitor memory usage
process = psutil.Process()
memory_mb = process.memory_info().rss / 1024 / 1024
print(f"Memory usage: {memory_mb:.1f} MB")

# Monitor prediction latency
start_time = time.time()
result = model.predict(features)
latency = time.time() - start_time
print(f"Prediction latency: {latency:.3f}s")
```

## 🔄 Deployment Checklist

### Pre-deployment
- [ ] Run all tests (`pytest`)
- [ ] Check code quality (`pre-commit`)
- [ ] Verify GPU functionality (`python verify_gpu.py`)
- [ ] Test model loading and prediction
- [ ] Validate database connections

### Deployment
- [ ] Backup existing models
- [ ] Update environment variables
- [ ] Deploy new model files
- [ ] Restart bot service
- [ ] Monitor logs for errors

### Post-deployment
- [ ] Test bot commands
- [ ] Monitor prediction accuracy
- [ ] Check error rates
- [ ] Verify GPU utilization
- [ ] Monitor database performance

## 🆘 Emergency Procedures

### Model Failure
1. Check model file integrity
2. Revert to backup model
3. Restart bot service
4. Monitor prediction accuracy
5. Investigate root cause

### Database Failure
1. Check database connectivity
2. Review connection pool status
3. Restart database service if needed
4. Verify data integrity
5. Monitor query performance

### Bot Failure
1. Check bot token validity
2. Review error logs
3. Restart bot service
4. Test basic commands
5. Monitor user interactions

This quick reference provides immediate access to essential information for maintaining and developing the Dota 2 Predictor project.
