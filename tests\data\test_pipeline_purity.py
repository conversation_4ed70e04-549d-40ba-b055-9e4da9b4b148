# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test data pipeline purity by scanning all CSV files for forbidden post-match features.
This ensures no data leakage occurs in draft-phase prediction datasets.
"""

import pytest
import pandas as pd
import os
import glob
import logging

from dataset.feature_schemas import FORBIDDEN_FEATURES, validate_features_for_draft_prediction

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def scan_csv_for_forbidden_features(csv_path):
    """Scan a CSV file for forbidden features."""
    try:
        # Read just the header to get column names
        df = pd.read_csv(csv_path, nrows=0)
        columns = df.columns.tolist()
        
        # Check for forbidden features
        forbidden_found = []
        for column in columns:
            for forbidden_pattern in FORBIDDEN_FEATURES:
                if forbidden_pattern in column.lower():
                    forbidden_found.append(column)
                    break
        
        return forbidden_found
    except Exception as e:
        logger.warning(f"Could not scan {csv_path}: {e}")
        return []

def test_draft_phase_dataset_purity():
    """Test that the draft-phase dataset contains no forbidden features."""
    
    draft_dataset_path = "dataset/train_data/all_data_draft_phase.csv"
    
    if not os.path.exists(draft_dataset_path):
        pytest.skip("Draft-phase dataset not found")
    
    forbidden_features = scan_csv_for_forbidden_features(draft_dataset_path)
    
    assert len(forbidden_features) == 0, \
        f"Draft-phase dataset contains forbidden features: {forbidden_features}"
    
    logger.info("✅ Draft-phase dataset purity test passed")

def test_all_training_data_csvs():
    """Test all CSV files in the training data directory for forbidden features."""
    
    train_data_dir = "dataset/train_data"
    if not os.path.exists(train_data_dir):
        pytest.skip("Training data directory not found")
    
    # Find all CSV files
    csv_files = glob.glob(os.path.join(train_data_dir, "*.csv"))
    
    if not csv_files:
        pytest.skip("No CSV files found in training data directory")
    
    results = {}
    
    for csv_file in csv_files:
        filename = os.path.basename(csv_file)
        forbidden_features = scan_csv_for_forbidden_features(csv_file)
        results[filename] = forbidden_features
        
        logger.info(f"Scanned {filename}: {len(forbidden_features)} forbidden features found")
    
    # Check results
    draft_phase_files = [f for f in results.keys() if 'draft' in f.lower()]
    
    # Draft-phase files should have no forbidden features
    for filename in draft_phase_files:
        forbidden = results[filename]
        assert len(forbidden) == 0, \
            f"Draft-phase file {filename} contains forbidden features: {forbidden}"
    
    # Log results for non-draft files (they may legitimately have forbidden features)
    non_draft_files = [f for f in results.keys() if 'draft' not in f.lower()]
    for filename in non_draft_files:
        forbidden = results[filename]
        if forbidden:
            logger.info(f"Non-draft file {filename} has {len(forbidden)} post-match features (expected)")
    
    logger.info("✅ Training data CSV scan completed")

def test_feature_schema_validation():
    """Test that feature schema validation works correctly."""
    
    # Test with known good features (hero IDs only)
    good_features = [
        'match_id',
        'radiant_player_1_hero_id',
        'radiant_player_2_hero_id',
        'dire_player_1_hero_id',
        'dire_player_2_hero_id',
        'radiant_win'
    ]
    
    is_valid, forbidden = validate_features_for_draft_prediction(good_features)
    assert is_valid, f"Good features incorrectly flagged as forbidden: {forbidden}"
    
    # Test with known bad features
    bad_features = [
        'match_id',
        'radiant_player_1_hero_id',
        'radiant_player_1_kills',  # This should be forbidden
        'radiant_player_1_gold_per_min',  # This should be forbidden
        'radiant_win'
    ]
    
    is_valid, forbidden = validate_features_for_draft_prediction(bad_features)
    assert not is_valid, "Bad features not detected by validation"
    assert len(forbidden) >= 2, f"Expected at least 2 forbidden features, got {len(forbidden)}: {forbidden}"
    
    logger.info("✅ Feature schema validation test passed")

def test_forbidden_patterns_coverage():
    """Test that forbidden patterns cover expected post-match statistics."""
    
    # These should all be caught by forbidden patterns
    test_features = [
        'radiant_player_1_kills',
        'dire_player_2_deaths',
        'radiant_player_3_assists',
        'dire_player_4_gold_per_min',
        'radiant_player_5_xp_per_min',
        'dire_player_1_net_worth',
        'radiant_player_2_last_hits',
        'dire_player_3_denies',
        'radiant_player_4_level',
        'dire_player_5_hero_damage',
        'radiant_player_1_tower_damage',
        'dire_player_2_teamfight_participation',
        'radiant_player_3_obs_placed',
        'dire_player_4_sen_placed',
        'radiant_player_5_roshans_killed',
        'dire_player_1_hero_healing'
    ]
    
    for feature in test_features:
        is_forbidden = False
        for pattern in FORBIDDEN_FEATURES:
            if pattern in feature:
                is_forbidden = True
                break
        
        assert is_forbidden, f"Feature '{feature}' should be forbidden but wasn't caught"
    
    logger.info("✅ Forbidden patterns coverage test passed")

def test_dataset_directory_structure():
    """Test that dataset directory has expected structure."""
    
    # Check main directories exist
    expected_dirs = [
        "dataset",
        "dataset/train_data"
    ]
    
    for dir_path in expected_dirs:
        assert os.path.exists(dir_path), f"Expected directory not found: {dir_path}"
    
    # Check for expected files
    train_data_dir = "dataset/train_data"
    expected_files = [
        "all_data_match_predict.csv",  # Full dataset
    ]
    
    for filename in expected_files:
        file_path = os.path.join(train_data_dir, filename)
        assert os.path.exists(file_path), f"Expected file not found: {file_path}"
    
    # Draft-phase dataset should exist if we've run the creation script
    draft_file = os.path.join(train_data_dir, "all_data_draft_phase.csv")
    if os.path.exists(draft_file):
        logger.info("Draft-phase dataset found")
        
        # Validate it has reasonable size
        df = pd.read_csv(draft_file)
        assert len(df) > 1000, f"Draft dataset too small: {len(df)} rows"
        assert len(df.columns) >= 12, f"Draft dataset too few columns: {len(df.columns)}"
    else:
        logger.warning("Draft-phase dataset not found - may need to be created")
    
    logger.info("✅ Dataset directory structure test passed")

if __name__ == "__main__":
    # Run tests directly
    test_feature_schema_validation()
    test_forbidden_patterns_coverage()
    test_dataset_directory_structure()
    test_draft_phase_dataset_purity()
    test_all_training_data_csvs()
