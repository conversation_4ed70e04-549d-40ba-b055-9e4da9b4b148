# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test statistical significance of model improvements.
Ensures improvements are not due to random chance.
"""

import pytest
import pandas as pd
import numpy as np
import logging
import os
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import xgboost as xgb

from ml.incremental_improvements import ModelVersionManager
from structure.helpers import prepare_draft_phase_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_statistical_significance_framework():
    """Test that statistical significance testing framework works."""
    
    # Create mock predictions for testing
    np.random.seed(42)
    n_samples = 1000
    
    # Create mock true labels
    y_true = np.random.choice([0, 1], n_samples)
    
    # Create baseline predictions (52% accuracy)
    baseline_correct_prob = 0.52
    y_pred_baseline = np.random.choice([0, 1], n_samples, 
                                     p=[1-baseline_correct_prob, baseline_correct_prob])
    # Adjust to match true labels more closely
    correct_mask = np.random.random(n_samples) < baseline_correct_prob
    y_pred_baseline[correct_mask] = y_true[correct_mask]
    
    # Create improved predictions (54% accuracy)
    improved_correct_prob = 0.54
    y_pred_improved = np.random.choice([0, 1], n_samples,
                                     p=[1-improved_correct_prob, improved_correct_prob])
    # Adjust to match true labels more closely
    correct_mask = np.random.random(n_samples) < improved_correct_prob
    y_pred_improved[correct_mask] = y_true[correct_mask]
    
    # Test statistical significance
    version_manager = ModelVersionManager()
    
    # Test McNemar's test
    mcnemar_result = version_manager.statistical_significance_test(
        y_true, y_pred_baseline, y_pred_improved, test_type="mcnemar"
    )
    
    assert "p_value" in mcnemar_result
    assert "significant" in mcnemar_result
    assert "contingency_table" in mcnemar_result
    assert mcnemar_result["test_type"] == "mcnemar"
    
    # Test bootstrap test
    bootstrap_result = version_manager.statistical_significance_test(
        y_true, y_pred_baseline, y_pred_improved, test_type="bootstrap"
    )
    
    assert "p_value" in bootstrap_result
    assert "significant" in bootstrap_result
    assert "confidence_interval" in bootstrap_result
    assert bootstrap_result["test_type"] == "bootstrap"
    
    logger.info(f"McNemar test p-value: {mcnemar_result['p_value']:.4f}")
    logger.info(f"Bootstrap test p-value: {bootstrap_result['p_value']:.4f}")
    logger.info("✅ Statistical significance framework test passed")

def test_improvement_evaluation():
    """Test model improvement evaluation logic."""
    
    version_manager = ModelVersionManager()
    
    # Mock baseline metrics
    baseline_metrics = {
        "accuracy": 0.52,
        "log_loss": 0.677,
        "roc_auc": 0.52,
        "brier_score": 0.25
    }
    
    # Test significant improvement
    improved_metrics = {
        "accuracy": 0.545,  # +2.5pp improvement
        "log_loss": 0.665,  # -0.012 improvement
        "roc_auc": 0.535,   # +1.5pp improvement
        "brier_score": 0.24
    }
    
    improvement_analysis = version_manager.evaluate_model_improvement(
        improved_metrics, baseline_metrics
    )
    
    assert improvement_analysis["accuracy_gain"] > 0.02
    assert improvement_analysis["accuracy_significant"] == True
    assert improvement_analysis["logloss_improved"] == True
    assert improvement_analysis["overall_improved"] == True
    
    # Test insufficient improvement
    marginal_metrics = {
        "accuracy": 0.521,  # +0.1pp improvement (too small)
        "log_loss": 0.675,  # +0.002 improvement
        "roc_auc": 0.522,
        "brier_score": 0.25
    }
    
    marginal_analysis = version_manager.evaluate_model_improvement(
        marginal_metrics, baseline_metrics
    )
    
    assert marginal_analysis["accuracy_significant"] == False
    assert marginal_analysis["overall_improved"] == False
    
    logger.info("✅ Improvement evaluation test passed")

def test_model_versioning():
    """Test model version management."""
    
    version_manager = ModelVersionManager()
    
    # Create a mock model (we'll use a simple XGBoost model)
    np.random.seed(42)
    X_mock = np.random.random((100, 10))
    y_mock = np.random.choice([0, 1], 100)
    
    dtrain = xgb.DMatrix(X_mock, label=y_mock)
    model = xgb.train(
        {"objective": "binary:logistic", "eval_metric": "logloss"},
        dtrain,
        num_boost_round=10
    )
    
    # Mock metrics and features
    mock_metrics = {
        "accuracy": 0.545,
        "log_loss": 0.665,
        "roc_auc": 0.535,
        "brier_score": 0.24,
        "model_type": "draft_model_v2_test"
    }
    
    mock_features = [f"feature_{i}" for i in range(10)]
    
    mock_improvement = {
        "accuracy_gain": 0.025,
        "accuracy_significant": True,
        "overall_improved": True
    }
    
    # Save model version
    version_dir = version_manager.save_model_version(
        model, "draft_v2_test", mock_metrics, mock_features, mock_improvement
    )
    
    # Verify files were created
    assert os.path.exists(version_dir)
    assert os.path.exists(os.path.join(version_dir, "draft_v2_test.pkl"))
    assert os.path.exists(os.path.join(version_dir, "metadata.json"))
    
    # Verify metadata content
    with open(os.path.join(version_dir, "metadata.json"), 'r') as f:
        metadata = json.load(f)
    
    assert metadata["version_name"] == "draft_v2_test"
    assert metadata["feature_count"] == 10
    assert len(metadata["features"]) == 10
    assert metadata["improvement_analysis"]["overall_improved"] == True
    
    # Cleanup
    import shutil
    if os.path.exists("models"):
        shutil.rmtree("models")
    
    logger.info("✅ Model versioning test passed")

def test_baseline_comparison_with_real_model():
    """Test comparison with actual draft model if available."""
    
    draft_model_path = "xgb_model_draft.pkl"
    if not os.path.exists(draft_model_path):
        pytest.skip("Draft model not found - run ml/create_model_draft_phase.py first")
    
    # Load draft dataset
    if not os.path.exists("dataset/train_data/all_data_draft_phase.csv"):
        pytest.skip("Draft dataset not found")
    
    df = pd.read_csv("dataset/train_data/all_data_draft_phase.csv")
    
    # Use a small subset for testing
    np.random.seed(42)
    test_size = min(1000, len(df))
    df_test = df.sample(n=test_size, random_state=42)
    
    # Prepare data
    df_prepared = prepare_draft_phase_data(df_test.copy(), "scaler_draft.pkl")
    X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
    y = df_prepared['radiant_win'].astype(int)
    
    # Split for testing
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # Load existing model
    model = xgb.Booster()
    model.load_model(draft_model_path)
    
    # Make predictions
    dtest = xgb.DMatrix(X_test)
    y_pred_proba = model.predict(dtest)
    y_pred = (y_pred_proba > 0.5).astype(int)
    
    # Calculate current metrics
    current_accuracy = accuracy_score(y_test, y_pred)
    
    # Test improvement evaluation framework
    version_manager = ModelVersionManager()
    baseline_metrics = version_manager.load_baseline_metrics()
    
    current_metrics = {
        "accuracy": current_accuracy,
        "log_loss": 0.677,  # Approximate
        "roc_auc": 0.52,    # Approximate
        "brier_score": 0.25
    }
    
    improvement_analysis = version_manager.evaluate_model_improvement(
        current_metrics, baseline_metrics
    )
    
    logger.info(f"Current accuracy: {current_accuracy:.4f}")
    logger.info(f"Baseline accuracy: {baseline_metrics.get('accuracy', 0.52):.4f}")
    logger.info(f"Improvement analysis: {improvement_analysis}")
    
    # The framework should work regardless of whether there's actual improvement
    assert "accuracy_gain" in improvement_analysis
    assert "overall_improved" in improvement_analysis
    
    logger.info("✅ Baseline comparison test passed")

def test_significance_thresholds():
    """Test that significance thresholds are appropriate."""
    
    version_manager = ModelVersionManager()
    
    # Test with very small improvement (should not be significant)
    baseline_metrics = {"accuracy": 0.520, "log_loss": 0.677, "roc_auc": 0.52}
    small_improvement = {"accuracy": 0.521, "log_loss": 0.676, "roc_auc": 0.521}  # 0.1pp improvement
    
    analysis = version_manager.evaluate_model_improvement(
        small_improvement, baseline_metrics, 
        min_accuracy_gain=0.02, max_logloss_increase=0.02
    )
    
    assert analysis["accuracy_significant"] == False, "Small improvement should not be significant"
    
    # Test with large improvement (should be significant)
    large_improvement = {"accuracy": 0.545, "log_loss": 0.660, "roc_auc": 0.545}  # 2.5pp improvement
    
    analysis = version_manager.evaluate_model_improvement(
        large_improvement, baseline_metrics,
        min_accuracy_gain=0.02, max_logloss_increase=0.02
    )
    
    assert analysis["accuracy_significant"] == True, "Large improvement should be significant"
    assert analysis["overall_improved"] == True, "Overall should be improved"
    
    logger.info("✅ Significance thresholds test passed")

if __name__ == "__main__":
    # Run tests directly
    test_statistical_significance_framework()
    test_improvement_evaluation()
    test_model_versioning()
    test_significance_thresholds()
    # test_baseline_comparison_with_real_model()  # Requires actual model
