# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Test the complete inference pipeline from draft data to probability output.
Validates the bot endpoint functionality for draft-phase predictions.
"""

import pytest
import pandas as pd
import numpy as np
import logging
import os
import xgboost as xgb

from structure.struct import Match, Team, Player, Hero
from structure.helpers import prepare_draft_phase_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_mock_match():
    """Create a mock match with draft data for testing."""

    # Create mock players with correct constructor
    radiant_players = []
    for i in range(5):
        player = Player(
            account_id=f"radiant_player_{i+1}",
            name=f"Radiant Player {i+1}",
            hero_id=i+1,  # Hero IDs 1-5
            team=0  # Radiant team
        )
        radiant_players.append(player)

    dire_players = []
    for i in range(5):
        player = Player(
            account_id=f"dire_player_{i+1}",
            name=f"Dire Player {i+1}",
            hero_id=i+6,  # Hero IDs 6-10
            team=1  # Dire team
        )
        dire_players.append(player)

    # Create mock teams
    radiant_team = Team(
        team_name="Radiant Team",
        team_id=1
    )

    dire_team = Team(
        team_name="Dire Team",
        team_id=2
    )

    # Add players to teams
    for player in radiant_players:
        radiant_team.add_player(player)

    for player in dire_players:
        dire_team.add_player(player)

    # Create mock match
    match = Match(
        match_id=12345,
        radiant_team_id=1,
        dire_team_id=2,
        league_id=1
    )

    # Set the teams
    match.radiant_team = radiant_team
    match.dire_team = dire_team

    return match

def test_draft_data_extraction():
    """Test that draft data can be extracted from a match."""
    
    match = create_mock_match()
    
    try:
        # This should work if the get_draft_data_for_prediction method exists
        draft_data, features = match.get_draft_data_for_prediction()
        
        # Validate the returned data
        assert isinstance(draft_data, pd.DataFrame), "Draft data should be a DataFrame"
        assert len(draft_data) == 1, "Should return exactly one row of data"
        assert isinstance(features, list), "Features should be a list"
        
        # Check for essential columns
        assert 'radiant_player_1_hero_id' in draft_data.columns, "Missing radiant hero ID"
        assert 'dire_player_1_hero_id' in draft_data.columns, "Missing dire hero ID"
        
        # Check hero ID values
        assert draft_data['radiant_player_1_hero_id'].iloc[0] == 1, "Incorrect radiant hero ID"
        assert draft_data['dire_player_1_hero_id'].iloc[0] == 6, "Incorrect dire hero ID"
        
        logger.info(f"✅ Draft data extraction test passed: {len(features)} features")
        
    except AttributeError:
        pytest.skip("get_draft_data_for_prediction method not implemented")
    except Exception as e:
        pytest.fail(f"Draft data extraction failed: {e}")

def test_draft_prediction_pipeline():
    """Test the complete draft prediction pipeline."""
    
    # Check if draft model exists
    model_path = "xgb_model_draft.pkl"
    if not os.path.exists(model_path):
        pytest.skip("Draft model not found - run ml/create_model_draft_phase.py first")
    
    # Create mock match
    match = create_mock_match()
    
    try:
        # Extract draft data
        draft_data, features = match.get_draft_data_for_prediction()
        
        # Load model
        model = xgb.Booster()
        model.load_model(model_path)
        
        # Prepare features for prediction
        X = draft_data.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
        
        # Make prediction
        dtest = xgb.DMatrix(X)
        prediction_proba = model.predict(dtest)
        
        # Validate prediction
        assert len(prediction_proba) == 1, "Should return exactly one prediction"
        prob = prediction_proba[0]
        
        assert 0 <= prob <= 1, f"Prediction probability should be between 0 and 1, got {prob}"
        assert not np.isnan(prob), "Prediction should not be NaN"
        assert not np.isinf(prob), "Prediction should not be infinite"
        
        logger.info(f"✅ Draft prediction pipeline test passed: probability={prob:.4f}")
        
    except AttributeError:
        pytest.skip("Draft prediction methods not implemented")
    except Exception as e:
        pytest.fail(f"Draft prediction pipeline failed: {e}")

def test_prediction_consistency():
    """Test that predictions are consistent for the same input."""
    
    model_path = "xgb_model_draft.pkl"
    if not os.path.exists(model_path):
        pytest.skip("Draft model not found")
    
    match = create_mock_match()
    
    try:
        # Make multiple predictions with the same input
        predictions = []
        
        for _ in range(5):
            draft_data, features = match.get_draft_data_for_prediction()
            
            model = xgb.Booster()
            model.load_model(model_path)
            
            X = draft_data.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
            dtest = xgb.DMatrix(X)
            prediction_proba = model.predict(dtest)
            
            predictions.append(prediction_proba[0])
        
        # All predictions should be identical
        for i in range(1, len(predictions)):
            assert abs(predictions[i] - predictions[0]) < 1e-10, \
                f"Predictions not consistent: {predictions[0]} vs {predictions[i]}"
        
        logger.info(f"✅ Prediction consistency test passed: {predictions[0]:.6f}")
        
    except AttributeError:
        pytest.skip("Draft prediction methods not implemented")
    except Exception as e:
        pytest.fail(f"Prediction consistency test failed: {e}")

def test_different_hero_combinations():
    """Test predictions with different hero combinations."""
    
    model_path = "xgb_model_draft.pkl"
    if not os.path.exists(model_path):
        pytest.skip("Draft model not found")
    
    # Create different hero combinations
    hero_combinations = [
        # Combination 1: Low hero IDs
        ([1, 2, 3, 4, 5], [6, 7, 8, 9, 10]),
        # Combination 2: High hero IDs  
        ([100, 101, 102, 103, 104], [105, 106, 107, 108, 109]),
        # Combination 3: Mixed
        ([1, 50, 75, 100, 125], [25, 60, 85, 110, 130])
    ]
    
    predictions = []
    
    try:
        model = xgb.Booster()
        model.load_model(model_path)
        
        for radiant_ids, dire_ids in hero_combinations:
            # Create match data manually
            match_data = {
                'match_id': 12345,
                'radiant_player_1_hero_id': radiant_ids[0],
                'radiant_player_2_hero_id': radiant_ids[1],
                'radiant_player_3_hero_id': radiant_ids[2],
                'radiant_player_4_hero_id': radiant_ids[3],
                'radiant_player_5_hero_id': radiant_ids[4],
                'dire_player_1_hero_id': dire_ids[0],
                'dire_player_2_hero_id': dire_ids[1],
                'dire_player_3_hero_id': dire_ids[2],
                'dire_player_4_hero_id': dire_ids[3],
                'dire_player_5_hero_id': dire_ids[4]
            }
            
            df = pd.DataFrame([match_data])
            df_prepared = prepare_draft_phase_data(df, "scaler_draft.pkl")
            
            X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
            dtest = xgb.DMatrix(X)
            prediction_proba = model.predict(dtest)
            
            predictions.append(prediction_proba[0])
        
        # Validate all predictions
        for i, prob in enumerate(predictions):
            assert 0 <= prob <= 1, f"Prediction {i} out of range: {prob}"
            assert not np.isnan(prob), f"Prediction {i} is NaN"
        
        # Predictions should vary (model is learning from hero differences)
        prediction_std = np.std(predictions)
        assert prediction_std > 0.001, f"Predictions too similar: std={prediction_std:.6f}"
        
        logger.info(f"✅ Different hero combinations test passed: predictions={[f'{p:.4f}' for p in predictions]}")
        
    except Exception as e:
        pytest.fail(f"Different hero combinations test failed: {e}")

def test_edge_cases():
    """Test edge cases in prediction."""
    
    model_path = "xgb_model_draft.pkl"
    if not os.path.exists(model_path):
        pytest.skip("Draft model not found")
    
    try:
        model = xgb.Booster()
        model.load_model(model_path)
        
        # Test case 1: All same heroes (should still work)
        match_data = {
            'match_id': 12345,
            'radiant_player_1_hero_id': 1,
            'radiant_player_2_hero_id': 1,
            'radiant_player_3_hero_id': 1,
            'radiant_player_4_hero_id': 1,
            'radiant_player_5_hero_id': 1,
            'dire_player_1_hero_id': 2,
            'dire_player_2_hero_id': 2,
            'dire_player_3_hero_id': 2,
            'dire_player_4_hero_id': 2,
            'dire_player_5_hero_id': 2
        }
        
        df = pd.DataFrame([match_data])
        df_prepared = prepare_draft_phase_data(df, "scaler_draft.pkl")
        
        X = df_prepared.drop(['radiant_win', 'match_id'], axis=1, errors='ignore')
        dtest = xgb.DMatrix(X)
        prediction_proba = model.predict(dtest)
        
        prob = prediction_proba[0]
        assert 0 <= prob <= 1, f"Edge case prediction out of range: {prob}"
        assert not np.isnan(prob), "Edge case prediction is NaN"
        
        logger.info(f"✅ Edge cases test passed: same heroes prediction={prob:.4f}")
        
    except Exception as e:
        pytest.fail(f"Edge cases test failed: {e}")

if __name__ == "__main__":
    # Run tests directly
    test_draft_data_extraction()
    test_different_hero_combinations()
    test_edge_cases()
    test_prediction_consistency()
    test_draft_prediction_pipeline()
