# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Final validation script for LSTM implementation.
Performs end-to-end validation of the complete LSTM pipeline.
"""

import os
import sys
import logging
import pandas as pd
import torch
import pickle
from pathlib import Path

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gensim.models import KeyedVectors
from ml.create_model_lstm import CBOWLSTMModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def validate_file_structure():
    """Validate that all required files exist."""
    logger.info("Validating file structure...")
    
    required_files = [
        "dataset/train_data/sequential_drafts.csv",
        "models/hero_embeddings.word2vec",
        "models/hero_embeddings.kv",
        "models/lstm_model.pth",
        "models/lstm_metrics.pkl",
        "ml/create_model_lstm.py",
        "scripts/generate_sequential_draft_dataset.py",
        "scripts/train_hero_embeddings.py",
        "tests/model_lstm/test_lstm_metrics.py",
        "tests/inference/test_lstm_endpoint.py",
        "docs/LSTM_MODEL_DOCUMENTATION.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"Missing files: {missing_files}")
        return False
    
    logger.info("✅ All required files present")
    return True

def validate_dataset():
    """Validate the sequential draft dataset."""
    logger.info("Validating sequential draft dataset...")
    
    try:
        df = pd.read_csv("dataset/train_data/sequential_drafts.csv")
        
        # Check required columns
        required_columns = ['match_id', 'pick_sequence', 'radiant_win']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"Missing required columns. Expected: {required_columns}, Got: {df.columns.tolist()}")
            return False
        
        # Check data types
        if not pd.api.types.is_integer_dtype(df['match_id']):
            logger.error("match_id should be integer type")
            return False
        
        if not pd.api.types.is_integer_dtype(df['radiant_win']):
            logger.error("radiant_win should be integer type")
            return False
        
        # Check pick sequences
        for idx, row in df.head(5).iterrows():  # Check first 5 rows
            try:
                picks = [int(x.strip()) for x in row['pick_sequence'].split(',')]
                if len(picks) != 10:
                    logger.error(f"Row {idx}: Expected 10 picks, got {len(picks)}")
                    return False
            except ValueError as e:
                logger.error(f"Row {idx}: Invalid pick sequence format: {e}")
                return False
        
        logger.info(f"✅ Dataset validation passed: {len(df)} matches")
        return True
        
    except Exception as e:
        logger.error(f"Dataset validation failed: {e}")
        return False

def validate_embeddings():
    """Validate hero embeddings."""
    logger.info("Validating hero embeddings...")
    
    try:
        # Test Word2Vec model
        from gensim.models import Word2Vec
        w2v_model = Word2Vec.load("models/hero_embeddings.word2vec")
        
        if w2v_model.wv.vector_size != 150:
            logger.error(f"Expected vector size 150, got {w2v_model.wv.vector_size}")
            return False
        
        # Test KeyedVectors
        kv = KeyedVectors.load("models/hero_embeddings.kv")
        
        if kv.vector_size != 150:
            logger.error(f"Expected vector size 150, got {kv.vector_size}")
            return False
        
        if len(kv.key_to_index) == 0:
            logger.error("No heroes in embeddings vocabulary")
            return False
        
        # Test embedding retrieval
        test_hero = list(kv.key_to_index.keys())[0]
        vector = kv[test_hero]
        
        if vector.shape != (150,):
            logger.error(f"Expected vector shape (150,), got {vector.shape}")
            return False
        
        logger.info(f"✅ Embeddings validation passed: {len(kv.key_to_index)} heroes, 150D vectors")
        return True
        
    except Exception as e:
        logger.error(f"Embeddings validation failed: {e}")
        return False

def validate_lstm_model():
    """Validate LSTM model."""
    logger.info("Validating LSTM model...")
    
    try:
        # Load model
        checkpoint = torch.load("models/lstm_model.pth", map_location='cpu')
        model = CBOWLSTMModel(
            embedding_dim=150,
            lstm_hidden_size=150,
            dropout_rate=0.3
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Check model architecture
        param_count = sum(p.numel() for p in model.parameters())
        if param_count == 0:
            logger.error("Model has no parameters")
            return False
        
        # Test forward pass
        batch_size = 2
        radiant_picks = torch.randn(batch_size, 5, 150)
        dire_picks = torch.randn(batch_size, 5, 150)
        
        with torch.no_grad():
            output = model(radiant_picks, dire_picks)
        
        if output.shape != (batch_size, 1):
            logger.error(f"Expected output shape ({batch_size}, 1), got {output.shape}")
            return False
        
        if not (torch.all(output >= 0) and torch.all(output <= 1)):
            logger.error("Output should be between 0 and 1")
            return False
        
        logger.info(f"✅ LSTM model validation passed: {param_count} parameters")
        return True
        
    except Exception as e:
        logger.error(f"LSTM model validation failed: {e}")
        return False

def validate_metrics():
    """Validate saved training metrics."""
    logger.info("Validating training metrics...")
    
    try:
        with open("models/lstm_metrics.pkl", "rb") as f:
            metrics = pickle.load(f)
        
        required_keys = ['accuracy', 'roc_auc', 'log_loss']
        for key in required_keys:
            if key not in metrics:
                logger.error(f"Missing metric: {key}")
                return False
        
        accuracy = metrics['accuracy']
        if not isinstance(accuracy, (int, float)) or accuracy < 0 or accuracy > 1:
            logger.error(f"Invalid accuracy value: {accuracy}")
            return False
        
        logger.info(f"✅ Metrics validation passed: Accuracy={accuracy:.4f}")
        return True
        
    except Exception as e:
        logger.error(f"Metrics validation failed: {e}")
        return False

def validate_integration():
    """Validate end-to-end integration."""
    logger.info("Validating end-to-end integration...")
    
    try:
        # Import the predictor class
        sys.path.append("tests/inference")
        from test_lstm_endpoint import LSTMPredictor
        
        # Initialize predictor
        predictor = LSTMPredictor()
        
        # Test prediction
        test_sequence = [31, 138, 121, 2, 56, 114, 137, 52, 8, 90]
        probability, predicted_winner = predictor.predict(test_sequence)
        
        # Validate output
        if not isinstance(probability, float):
            logger.error(f"Expected float probability, got {type(probability)}")
            return False
        
        if not (0 <= probability <= 1):
            logger.error(f"Probability {probability} should be between 0 and 1")
            return False
        
        if predicted_winner not in ["Radiant", "Dire"]:
            logger.error(f"Invalid predicted winner: {predicted_winner}")
            return False
        
        logger.info(f"✅ Integration validation passed: {probability:.4f} ({predicted_winner})")
        return True
        
    except Exception as e:
        logger.error(f"Integration validation failed: {e}")
        return False

def run_test_suite():
    """Run the complete test suite."""
    logger.info("Running test suite...")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/model_lstm/", "tests/inference/test_lstm_endpoint.py", 
            "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            logger.info("✅ All tests passed")
            return True
        else:
            logger.error(f"Tests failed:\n{result.stdout}\n{result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False

def validate_lstm_implementation():
    """Main validation function."""
    logger.info("Starting LSTM implementation validation")
    logger.info("=" * 60)
    
    validations = [
        ("File Structure", validate_file_structure),
        ("Dataset", validate_dataset),
        ("Hero Embeddings", validate_embeddings),
        ("LSTM Model", validate_lstm_model),
        ("Training Metrics", validate_metrics),
        ("End-to-End Integration", validate_integration),
        ("Test Suite", run_test_suite),
    ]
    
    results = []
    for name, validation_func in validations:
        logger.info(f"\n--- {name} Validation ---")
        try:
            result = validation_func()
            results.append((name, result))
            if result:
                logger.info(f"✅ {name} validation PASSED")
            else:
                logger.error(f"❌ {name} validation FAILED")
        except Exception as e:
            logger.error(f"❌ {name} validation ERROR: {e}")
            results.append((name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("VALIDATION SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{name:.<30} {status}")
    
    logger.info("-" * 60)
    logger.info(f"Total: {passed}/{total} validations passed")
    
    if passed == total:
        logger.info("🎉 ALL VALIDATIONS PASSED! LSTM implementation is ready for production.")
        return True
    else:
        logger.error(f"❌ {total - passed} validation(s) failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    try:
        success = validate_lstm_implementation()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Validation script failed: {e}")
        sys.exit(1)
